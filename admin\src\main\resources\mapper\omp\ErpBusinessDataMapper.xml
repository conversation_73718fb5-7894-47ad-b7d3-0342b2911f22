<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.admin.omp.mapper.ErpBusinessDataMapper">
	<resultMap id="BaseDisCountResultMap" type="com.tzx.admin.omp.entity.ErpDisCountData">
		<result column="bbrq" property="bbrq" jdbcType="VARCHAR" />
		<result column="money" property="money" jdbcType="DECIMAL" />
		<result column="fdjgxh" property="fdjgxh" jdbcType="INTEGER" />
	</resultMap>

	<resultMap id="BasePaymentResultMap" type="com.tzx.admin.omp.entity.ErpPaymentData">
		<result column="source" property="source" jdbcType="VARCHAR" />
		<result column="counts" property="count" jdbcType="INTEGER" />
		<result column="bbrq" property="bbrq" jdbcType="VARCHAR" />
		<result column="fdjgxh" property="fdjgxh" jdbcType="INTEGER" />
		<result column="fkje" property="fkje" jdbcType="DECIMAL" />
		<result column="omp_id" property="pmid" jdbcType="BIGINT" />
	</resultMap>

	<select id="queryErpDisCountData" resultMap="BaseDisCountResultMap">
		select TO_CHAR(BBRQ, 'yyyy-MM-dd') as BBRQ,SUM(yhje) as money,fdjgxh FROM TZXERP.BB_YH_ZD_ZL WHERE bbrq = date'${bbrq}' and fdjgxh = #{jgxh,jdbcType=INTEGER}
		group by fdjgxh, bbrq;
	</select>


	<select id="queryErPaymentData" resultMap="BasePaymentResultMap">


		select pay.bbrq, sum(fkje) as fkje, pay.fdjgxh, pay.source, count(distinct pay.zdbh) as counts,omp_id
		from (
		select TO_CHAR(ek.BBRQ, 'yyyy-MM-dd') as BBRQ, fkje, ek.fdjgxh, es.omp_source as source, ek.zdbh,el.omp_id
		from tzxerp.his_fdzdfkfsk ek
		left join
		tzxerp.his_Fdzdk ef
		on ek.fdjgxh = ef.fdjgxh and ek.bbrq = ef.bbrq and ek.zdbh = ef.zdbh
		left join tzxerp.erp_omp_pay_rel el
		on ek.jzid = el.tzx_pay_id
		left join tzxerp.erp_omp_source es on ef.source = es.tzx_source
		where ek.bbrq = date'${bbrq}'
		and ek.FDJGXH = #{jgxh,jdbcType=INTEGER}) pay
		group by bbrq, fdjgxh, source,omp_id;

	</select>

	<select id="queryErpShopData" resultType="com.tzx.admin.omp.entity.ErpShopData">
		SELECT distinct TO_CHAR(EK.BBRQ, 'yyyy-MM-dd') AS BBRQ, EK.FDJGXH AS JGXH FROM TZXERP.ERP_FDZDK  EK
		<if test="dayEndFlag != null and dayEndFlag == 1 ">
			LEFT JOIN TZXERP.BB_ORGAN_OEC BO ON EK.BBRQ = BO.BBRQ AND EK.FDJGXH = BO.JGXH
		</if>
			<where>
				ek.bbrq = date'${bbrq}'
				<if test="dayEndFlag != null and dayEndFlag == 1 ">
					AND bo.is_ok = 'Y'
				</if>
			</where>
		UNION ALL
		select to_char(hk.bbrq, 'yyyy-MM-dd') as bbrq, hk.fdjgxh as jgxh from tzxerp.his_fdzdk  hk
		<if test="dayEndFlag != null and dayEndFlag == 1 ">
			left join tzxerp.bb_organ_oec bo on hk.bbrq = bo.bbrq and hk.fdjgxh = bo.jgxh
		</if>
		<where>
			hk.bbrq = date'${bbrq}'
			<if test="dayEndFlag != null and dayEndFlag == 1 ">
				AND bo.is_ok = 'Y'
			</if>
		</where>
	</select>

	<select id="queryDayEndNoOssData" resultType="com.tzx.admin.omp.entity.ErpShopData">
		select distinct data.bbrq, data.jgxh
		from (SELECT EK.BBRQ,
					 EK.FDJGXH AS JGXH
			  FROM TZXERP.ERP_FDZDK EK
					   LEFT JOIN
				   TZXERP.BB_ORGAN_OEC BO
				   ON EK.BBRQ = BO.BBRQ
					   AND EK.FDJGXH = BO.JGXH
			  WHERE ek.bbrq = date'${bbrq}'
				AND bo.is_ok = 'Y'
			  UNION
				  ALL
			  select hk.bbrq,
					 hk.fdjgxh as jgxh
			  from tzxerp.his_fdzdk hk
					   left join
				   tzxerp.bb_organ_oec bo
				   on hk.bbrq = bo.bbrq
					   and hk.fdjgxh = bo.jgxh
			  WHERE hk.bbrq = date'${bbrq}'
				AND bo.is_ok = 'Y') data
				 left join
			 tzxerp.erp_omp_pos_bill eopb on data.bbrq = eopb.bbrq and data.JGXH = eopb.jgxh
		where ossstatus is null or ossstatus !=1;
	</select>
</mapper>
package com.tzx.admin.conf;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hccake.ballcat.common.oss.DefaultOssTemplate;
import com.hccake.ballcat.common.oss.OssProperties;
import com.hccake.ballcat.common.oss.OssTemplate;
import com.tzx.admin.entity.ErpThirdInfo;
import com.tzx.admin.job.OMPSyncJob;
import com.tzx.admin.omp.OmpProperties;
import com.tzx.admin.omp.SyncBusinessDataService;
import com.tzx.admin.omp.SyncPayToOmpService;
import com.tzx.admin.omp.entity.ErpOmpSourceEntity;
import com.tzx.admin.omp.entity.OmpShopEntity;
import com.tzx.admin.omp.event.OmpPushEventListener;
import com.tzx.admin.omp.mapper.*;
import com.tzx.admin.omp.service.SyncBusinessDataServiceImpl;
import com.tzx.admin.omp.service.SyncPayToOmpServiceImpl;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.tzx.admin.constants.ErpThirdTypeConstant.*;
import static com.tzx.admin.constants.MerChantConstant.TRUE;
import static com.tzx.admin.constants.OmpConstant.*;

@Configuration
@ConditionalOnProperty(name = "omp.enable", havingValue = TRUE)
public class OmpConf {

	@Bean("syncPayToOmpService")
	@ConditionalOnProperty(name = "omp.profile", havingValue = TRUE)

	public SyncPayToOmpService syncPayToOmpService(ErpOmpPayRelMapper erpOmpPayRelMapper,
			@Qualifier("profileOmpOssTemplate") OssTemplate ompOssTemplate, OmpProperties ompProperties,
			Map<String, String> welifeSettingsMap) {
		{
			return new SyncPayToOmpServiceImpl(erpOmpPayRelMapper, ompOssTemplate, ompProperties, welifeSettingsMap);
		}
	}

	@Primary
	@Bean(name = "profileOmpOssTemplate")
	@ConditionalOnProperty(name = "omp.profile", havingValue = TRUE)
	public OssTemplate profileOmpOssTemplate(Map<String, String> ompOssBucket) {
		OssProperties ossProperties = genOssProperties(ompOssBucket);
		ossProperties.setPathStyleAccess(true);
		ossProperties.setEndpoint(ompOssBucket.get(PROFILES_OMP_ENDPOINT));
		ossProperties.setBucket(ompOssBucket.get(OMP_PROFILE_BUCKET_NAME));
		return new DefaultOssTemplate(ossProperties);
	}

	@Bean("ompOssBucket")
	public Map<String, String> ossProperties(ErpThirdInfoMapper erpThirdInfoMapper) {
		ErpThirdInfo erpThirdInfo = new ErpThirdInfo();
		erpThirdInfo.setThirdtype(OMP_OSS_TYPE);
		List<ErpThirdInfo> erpThirdInfos = erpThirdInfoMapper.selectErpThirdInfoListByType(erpThirdInfo);
		return erpThirdInfos.stream()
			.collect(Collectors.toMap(ErpThirdInfo::getThirdcode, ErpThirdInfo::getThirdvalue, (a, b) -> b));

	}

	@Bean(name = "posOmpOssTemplate")
	@ConditionalOnProperty(name = "omp.profile", havingValue = TRUE)
	public OssTemplate posOssTemplate(Map<String, String> ompOssBucket) {
		OssProperties ossProperties = genOssProperties(ompOssBucket);
		ossProperties.setEndpoint(ompOssBucket.get(ORDERS_OMP_ENDPOINT));
		ossProperties.setBucket(ompOssBucket.get(OMP_POS_BUCKET_NAME));
		ossProperties.setPathStyleAccess(true);
		return new DefaultOssTemplate(ossProperties);
	}

	@Bean("ompProperties")
	public OmpProperties ompProperties(ErpThirdInfoMapper erpThirdInfoMapper) {
		QueryWrapper<ErpThirdInfo> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("thirdtype", "DZZX");
		queryWrapper.select("distinct thirdcode,thirdvalue");
		List<ErpThirdInfo> erpThirdInfos = erpThirdInfoMapper.selectList(queryWrapper);
		Map<String, String> collect = erpThirdInfos.stream()
			.collect(Collectors.toMap(ErpThirdInfo::getThirdcode, ErpThirdInfo::getThirdvalue, (a, b) -> b));
		OmpProperties ompProperties = new OmpProperties();
		ompProperties.setAppId(collect.get(APPID));
		ompProperties.setOpenApiUrl(collect.get(OPEN_API_URL));
		ompProperties.setAppKey(collect.get(APPKEY));
		ompProperties.setMerchantId(collect.get(MID));
		return ompProperties;
	}

	@Bean("tzxOmpSourceMapping")
	@ConditionalOnProperty(name = "omp.billData", havingValue = TRUE)
	public Map<String, String> tzxOmpSourceMapping(ErpOmpSourceMapper erpOmpSourceMapper) {
		QueryWrapper<ErpOmpSourceEntity> queryWrapper = new QueryWrapper<>();
		List<ErpOmpSourceEntity> erpOmpSourceEntities = erpOmpSourceMapper.selectList(queryWrapper);
		return erpOmpSourceEntities.stream()
			.collect(Collectors.toMap(ErpOmpSourceEntity::getTzxSource, ErpOmpSourceEntity::getOmpSource, (a, b) -> b));
	}

	@Bean("tzxOmpShopMapping")
	@ConditionalOnProperty(name = "omp.billData", havingValue = TRUE)
	public Map<Integer, OmpShopEntity> tzxOmpShopMapping(ErpOmpSourceMapper erpOmpSourceMapper) {
		List<OmpShopEntity> ompShopEntities = erpOmpSourceMapper.queryOmpShopList(new OmpShopEntity());
		return ompShopEntities.stream().collect(Collectors.toMap(OmpShopEntity::getShopId, a -> a, (a, b) -> b));
	}

	@Bean("syncBusinessDataService")
	@ConditionalOnProperty(name = "omp.billData", havingValue = TRUE)
	public SyncBusinessDataService syncBusinessDataService(OmpProperties properties,
			ErpOmpPosBillMapper erpOmpPosBillMapper, ApplicationEventPublisher applicationEventPublisher,
			ErpBusinessDataMapper erpBusinessDataMapper, SqlSessionFactory sqlSessionFactory,
			Map<Integer, OmpShopEntity> tzxOmpShopMapping,
			@Qualifier("posOmpOssTemplate") OssTemplate posOmpOssTemplate) {
		return new SyncBusinessDataServiceImpl(properties, erpOmpPosBillMapper, applicationEventPublisher,
				erpBusinessDataMapper, sqlSessionFactory, tzxOmpShopMapping, posOmpOssTemplate);
	}

	@Bean("ompSyncJob")
	@ConditionalOnProperty(name = "omp.enable", havingValue = TRUE)
	public OMPSyncJob ompSyncJob(SyncPayToOmpService syncPayToOmpService,
			SyncBusinessDataService syncBusinessDataService) {
		return new OMPSyncJob(syncPayToOmpService, syncBusinessDataService);
	}

	@Bean("ompPushEventListener")
	public OmpPushEventListener ompPushEventListener() {
		return new OmpPushEventListener();
	}

	@Bean("menuOmpOssTemplate")
	public OssTemplate menuOmpOssTemplate(Map<String, String> ompOssBucket) {
		final OssProperties ossProperties = genOssProperties(ompOssBucket);
		ossProperties.setPathStyleAccess(false);
		return new DefaultOssTemplate(ossProperties);
	}

	@Bean("ompBidMap")
	public Map<String, Integer> ompBidMap(Map<Integer, OmpShopEntity> tzxOmpShopMapping) {
		return tzxOmpShopMapping.values()
			.stream()
			.collect(Collectors.toMap(OmpShopEntity::getOmpBranchId, OmpShopEntity::getBranchId, (a, b) -> b));
	}

	@Bean("welifeSettingsMap")
	public Map<String, String> welifeSettingsMap(ErpThirdInfoMapper erpThirdInfoMapper) {
		ErpThirdInfo erpThirdInfo = new ErpThirdInfo();
		erpThirdInfo.setThirdtype("ACEWILLMEMBER");
		List<ErpThirdInfo> erpThirdInfos = erpThirdInfoMapper.selectErpThirdInfoListByType(erpThirdInfo);
		if (erpThirdInfos == null || erpThirdInfos.isEmpty()) {
			return new HashMap<>();
		}
		return erpThirdInfos.stream()
			.collect(Collectors.toMap(ErpThirdInfo::getThirdcode, ErpThirdInfo::getThirdvalue, (a, b) -> b));
	}

	private OssProperties genOssProperties(Map<String, String> ompOssBucket) {
		String accessKey = ompOssBucket.get(ACCESS_KEY_ID);
		String endPoint = ompOssBucket.get(ENDPOINT);
		String accessSecret = ompOssBucket.get(ACCESS_KEY_SECRET);
		OssProperties ossProperties = new OssProperties();
		ossProperties.setPathStyleAccess(true);
		ossProperties.setAccessSecret(accessSecret);
		ossProperties.setAccessKey(accessKey);
		ossProperties.setEndpoint(endPoint);
		ossProperties.setRegion("cn-beijing");
		return ossProperties;
	}

}

package com.tzx.admin.omp.service;

import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hccake.ballcat.common.oss.OssTemplate;
import com.tzx.admin.enums.OmpPayType;
import com.tzx.admin.omp.OmpProperties;
import com.tzx.admin.omp.SyncPayToOmpService;
import com.tzx.admin.omp.entity.ErpOmpPayRel;
import com.tzx.admin.omp.entity.ErpPayment;
import com.tzx.admin.omp.entity.ErpWelifePayRel;
import com.tzx.admin.omp.entity.OmpPayment;
import com.tzx.admin.omp.mapper.ErpOmpPayRelMapper;
import com.tzx.admin.utils.HttpClientUtil;
import com.tzx.admin.utils.WSHUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class SyncPayToOmpServiceImpl implements SyncPayToOmpService {

	private final ErpOmpPayRelMapper erpOmpPayRelMapper;

	private final OssTemplate ompOssTemplate;

	private final OmpProperties ompProperties;

	private final Map<String, String> welifeSettingsMap;

	public SyncPayToOmpServiceImpl(ErpOmpPayRelMapper erpOmpPayRelMapper,
			@Qualifier("profileOmpOssTemplate") OssTemplate ompOssTemplate, OmpProperties ompProperties,
			@Qualifier("welifeSettingsMap") Map<String, String> welifeSettingsMap) {

		this.erpOmpPayRelMapper = erpOmpPayRelMapper;
		this.ompOssTemplate = ompOssTemplate;
		this.ompProperties = ompProperties;
		this.welifeSettingsMap = welifeSettingsMap;
	}

	// private OssTemplate initOmpOssTemplate(){
	// if(ompOssTemplate == null){
	//// bucket: profiles-omp
	//// endpoint: oss-cn-beijing.aliyuncs.com
	//// outendpoint: oss-cn-beijing.aliyuncs.com
	//// accessKeyId: LTAI4GJ2wLfqvTcekrrmPE2a
	//// accessKeySecret: ******************************
	// OssProperties ossProperties = new OssProperties();
	// ossProperties.setPathStyleAccess(true);
	// ossProperties.setAccessKey("LTAI4GJ2wLfqvTcekrrmPE2a");
	// ossProperties.setAccessSecret("******************************");
	// ossProperties.setEnabled(true);
	// ossProperties.setEndpoint("oss-cn-beijing.aliyuncs.com");
	// ossProperties.setRegion("cn-beijing");
	// ossProperties.setBucket("profiles-omp");
	// ompOssTemplate = new DefaultOssTemplate(ossProperties);
	// }
	// return ompOssTemplate;
	// }

	@Override
	public void sync() {
		erpOmpPayRelMapper.syncRel();
		final LambdaQueryWrapper<ErpOmpPayRel> queryWrapper = new LambdaQueryWrapper<>();
		final List<ErpOmpPayRel> ompPayRelList = erpOmpPayRelMapper.selectList(queryWrapper);
		final Map<String, ErpOmpPayRel> ompPayRelMap = ompPayRelList.stream()
			.collect(Collectors.toMap(ErpOmpPayRel::getTzxPayId, e -> e));
		log.info("ompPayRelMap:{}", ompPayRelMap);
		boolean hasUnsynced = ompPayRelList.stream().anyMatch(ompPayRel -> "0".equals(ompPayRel.getSyncStatus()));
		if (hasUnsynced) {
			// initOmpOssTemplate();
			log.info("有未同步的支付方式");
			// ErpOmpPayRel erpOmpPayRel = new ErpOmpPayRel();
			// erpOmpPayRel.setSyncStatus("1");
			// erpOmpPayRelMapper.update(erpOmpPayRel, queryWrapper);
			final List<ErpPayment> erpPaymentList = erpOmpPayRelMapper.selectErpPaymentList();
			log.info("erpPaymentList:{}", erpPaymentList);
			final List<OmpPayment> ompPaymentList = erpPaymentList.stream()
				.map(erpPayment -> toOmpPayment(erpPayment, ompPayRelMap))
				.collect(Collectors.toList());
			log.info("ompPaymentList:{}", ompPaymentList);
			try {
				Path path = Paths.get("/tmp/payments.json");
				if (Files.exists(path)) {
					Files.delete(path);
				}
				Files.write(path, JSONUtil.toJsonStr(ompPaymentList).getBytes(StandardCharsets.UTF_8));
				String key = "payment/" + ompProperties.getMerchantId() + "/V1.0/paymentinfo.json";
				log.info("ossProperties,{}", ompOssTemplate.getOssProperties());

				ompOssTemplate.putObject("notomp", key, path.toFile());
			}
			catch (Exception e) {
				log.error("写入文件失败", e);
			}
		}
	}

	private OmpPayment toOmpPayment(final ErpPayment erpPayment, final Map<String, ErpOmpPayRel> ompPayRelMap) {
		final OmpPayment ompPayment = new OmpPayment();
		ompPayment.setPmid(ompPayRelMap.get(erpPayment.getId() + "").getOmpId());
		ompPayment.setPmno(erpPayment.getFkfsbh());
		ompPayment.setPmname(erpPayment.getFkfsmc());
		try {
			ompPayment.setPmnamesp(PinyinUtil.getPinyin(erpPayment.getFkfsmc(), ""));
		}
		catch (Exception e) {
			log.error(erpPayment.getFkfsmc() + ",获取拼音失败");
		}
		try {
			List<String> ompOrgIdList = erpOmpPayRelMapper.selectOrgOmpIdListByPayId(erpPayment.getId());
			if (null != ompOrgIdList && ompOrgIdList.size() > 0) {
				ompPayment.setOgnids(StringUtils.join(ompOrgIdList, ","));
			}
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		// 中台 0是生效 1是失效
		ompPayment.setStatus("Y".equals(erpPayment.getSfyx()) ? 0 : 1);
		// 中台 -是否参与开发票 0--否 1--是
		ompPayment.setIsnoinvoiceflag("Y".equals(erpPayment.getSfkfp()) ? 1 : 0);
		ompPayment.setOrder(NumberUtils.toInt("" + erpPayment.getPxbh(), 0));

		if (StringUtils.isNotEmpty(erpPayment.getFklxid())) {
			Integer ompPmType = OmpPayType.getOmpPmType(erpPayment.getFklxid());
			ompPayment.setPmtype(ompPmType == null ? 9 : ompPmType);
		}
		return ompPayment;
	}

	@Override
	public void syncWelifePay() {

		if (null == welifeSettingsMap || welifeSettingsMap.isEmpty()) {
			return;
		}

		List<ErpPayment> erpPaymentList = erpOmpPayRelMapper.selectToSyncWelifePaymentList();

		log.info("待同步到微生活的 erpPaymentList:{}", erpPaymentList);

		if (null == erpPaymentList || erpPaymentList.isEmpty()) {
			return;
		}
		for (ErpPayment erpPayment : erpPaymentList) {
			Long welifePayId = syncPaymentToWelife(erpPayment);
			if (null == welifePayId || welifePayId < 0) {
				log.error(erpPayment.getFkfsmc() + "同步到微生活失败");
				continue;
			}

			ErpWelifePayRel payRel = new ErpWelifePayRel();
			payRel.setWelifeId(welifePayId);
			payRel.setTzxPayId(erpPayment.getId());
			erpOmpPayRelMapper.saveWelifePayRef(payRel);
		}
	}

	private Long syncPaymentToWelife(ErpPayment erpPayment) {
		try {
			String url = "/deal/addpaytype"; // 同步支付方式接口
			// 查询配置参数
			String urlPath = "";
			// 判断是否开启
			if (welifeSettingsMap.get("OPEN") != null && welifeSettingsMap.get("OPEN").trim().equals("1")) {
				urlPath = welifeSettingsMap.get("URL") + url;// 接口url
			}
			Long time = System.currentTimeMillis();
			JSONObject jsonParam = new JSONObject();
			jsonParam.put("payName", erpPayment.getFkfsmc());
			jsonParam.put("payNumber", 100 + erpPayment.getId());
			jsonParam.put("edit", 0);

			// 组装微生活会员接口的请求参数
			Map<String, String> reqMap = WSHUtils.getPathParamMap(jsonParam, time, welifeSettingsMap);
			log.info("微生活接口请求地址：" + urlPath);
			log.info("微生活接口请求参数：" + reqMap.toString());
			String resultStr = HttpClientUtil.postSSLUrlWithParams(urlPath, reqMap);
			log.info("微生活会员接口返回消息：" + resultStr);
			if (resultStr.length() == 0) {
				return -1L;
			}
			JSONObject resultJson = JSONObject.parseObject(resultStr);
			String errcode = resultJson.getString("errcode");
			if (!"0".equals(errcode)) {
				return -1L;
			}
			return 100 + erpPayment.getId();
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
			return -1L;
		}
	}

}

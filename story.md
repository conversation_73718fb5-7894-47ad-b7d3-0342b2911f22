# 需求内容
【【鼎友】 对账中心 - 对接OMP支持 - 推送对账中心门店优惠中, 应该增加奉送的金额】https://www.tapd.cn/49924589/prong/tasks/view/1149924589001664805

# 需求内容
1. 推送对账中的门店优惠中, 应该增加奉送的金额
2. 一笔订单的奉送金额逻辑, 查询逻辑如下:
```sql
select TO_CHAR(ek.BBRQ, 'yyyy-MM-dd') as BBRQ, fdjgxh, sum(t.fsje) as fkje
from tzxerp.his_fdzdk t
where t.bbrq = date'2024-12-04'
  and t.fdjgxh = 1236
group by bbrq, fdjgxh;
```
3. 不计收入的付款金额, 也需要体现在支付的列表中, 具体要求如下:
    - 记收入和不计收入的付款金额, 只进行一次查询
    - 所有付款金额, 都还是按照付款方式进行汇总
    - 不计收入的付款金额, 生成oss对象时, 应付金额=付款金额, 实付金额=0

4. 计收入的付款金额逻辑也需要修改, 需要区分应付金额和实付金额, 具体要求如下:
    - BigDecimal精度为4位小数
    - 你生成的代码注释中, 如果有乱码, 请进行修改
    - 实付金额是现有逻辑
    - 应付金额需要修改, 应付金额 = 实付金额 - 该付款对应的优惠金额
    - 优惠表是 tzxerp.his_fdzdmxqy, 其中 cpje字段记录了优惠金额 (是负数)
    - 优惠表.yl1字段 和 付款表的 fkhm , bbrq, fdjgxh 可以进行关联
    - 查询付款列表时, 需要统计出应付金额 即 fkje-cpje

CREATE TABLE tzxerp.omp_boh_id_ref (
       name VARCHAR(100),
       brand_id INTEGER,
       boh_id INTEGER,
       omp_id INTEGER,
       PRIMARY KEY (name, omp_id)
);

-- 创建索引以提高查询性能
CREATE INDEX idx_name_brand_boh ON tzxerp.omp_boh_id_ref(name, brand_id, boh_id);

DROP VIEW "tzxerp"."vst_omp_boh_id_ref";

-- 创建视图

DROP VIEW "tzxerp"."vst_omp_boh_id_ref";

CREATE VIEW "tzxerp"."vst_omp_boh_id_ref" AS
SELECT concat(eo.jgxh, '-', obir.name, '-', obir.omp_id) AS id,
       eo.jgxh,
       ed.ompid                                          AS omp_brand_id,
       obir.name,
       obir.brand_id,
       obir.boh_id,
       obir.omp_id
FROM ((tzxerp.erp_orginfo eo
    LEFT JOIN tzxerp.erp_orgbrand ed ON ((eo.ppsx = ed.id)))
    LEFT JOIN tzxerp.omp_boh_id_ref obir ON (((obir.brand_id)::numeric = ed.id)))
WHERE ((eo.ompid IS NOT NULL) AND (ed.ompid IS NOT NULL));

ALTER TABLE "tzxerp"."vst_omp_boh_id_ref"
    OWNER TO "tzxdb";
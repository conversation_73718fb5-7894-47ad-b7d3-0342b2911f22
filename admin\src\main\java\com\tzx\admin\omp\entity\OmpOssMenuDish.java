package com.tzx.admin.omp.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class OmpOssMenuDish {

	private String dishesname;

	private String dishesno;

	private Integer packflag;

	private String dishesnamee;

	private List<DishtopsItem> dishtops = new ArrayList<>();

	private List<MenudishespriceItem> menudishesprice = new ArrayList<>();

	private Integer isnoweighing;

	private Integer onlypackflag;

	private Integer isautoloadingpeople;

	private String brandid;

	private Integer startnum;

	private String mnemonic;

	private Integer changnameflag;

	private Integer order;

	private List<PackageDetailed> packagedetailed = new ArrayList<>();

	private List<AuxiliaryDishesType> auxiliarydishestype = new ArrayList<>();

	private String dishPadPicurl;

	private Integer isstatisticspepoledishes;

	private Integer issuitdcb;

	private Integer dishesid;

	private Integer isautoloadingtable;

	private String takeoutPicUrl;

	private String picture;

	private List<PracticesItem> practices = new ArrayList<>();

	private String picurl;

	private Integer lengthtime;

	private Integer changpriceflag;

	private List<MenudishespracticesItem> menudishespractices = new ArrayList<>();

	private Integer isjoinclick;

	private Integer rcid;

	private Integer sumnum;

	private List<MenudishescostItem> menudishescost = new ArrayList<>();

	private Integer sauceFlag;

	private Integer status;

	@JsonIgnore
	private Integer dwid;

	@Data
	public static class DishtopsItem {

		private Integer tpid;

		private Integer dishesid;

		private Integer menuid;

		private Integer id;

	}

	@Data
	public static class PracticesItem {

		private Integer price;

		private Integer practiceid;

		private Integer dishesid;

	}

	@Data
	public static class MenudishespracticesItem {

		private Integer price;

		private Integer practiceid;

		private Integer dishesid;

		private Integer menuid;

		private Integer id;

	}

	@Data
	public static class MenudishescostItem {

		private Integer ratedcost;

		private Integer dishesid;

		private Integer dnid;

		private Integer id;

	}

	@Data
	public static class PackageDetailed {

		private Double fdiMaxcount;

		private Double fdiCount;

		private Integer fdiPackaged;

		private Integer fdiPdid;

		private Integer fdiPracticeid;

		private Integer fdiDnid;

		private Integer fdiPdtype;

		private Integer fdiDishesid;

		private Integer fdbIsdefaultdishes;

		private Integer fdiRpdid;

		private Double fdiRpjiajiaamount;

	}

	@Data
	public static class AuxiliaryDishesType {

		private Integer isprompty;

		private Integer maxcount;

		private Integer mincount;

		private Double count;

		private String rpdname;

		private Integer dishesid;

		private Integer rpdid;

		private Integer rpdtype;

	}

}

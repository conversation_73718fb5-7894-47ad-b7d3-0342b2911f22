package com.tzx.admin.omp.mapper;

import com.tzx.admin.omp.entity.OmpBohIdRef;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OmpBohIdRefMapper {

	OmpBohIdRef findByNameAndBrandIdAndBohId(@Param("name") String name, @Param("brandId") Integer brandId,
			@Param("bohId") Integer bohId);

	Integer findMaxOmpIdByName(@Param("name") String... name);

	int insert(OmpBohIdRef ompBohIdRef);

}
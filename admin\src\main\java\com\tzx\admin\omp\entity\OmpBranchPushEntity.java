package com.tzx.admin.omp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Objects;

@AllArgsConstructor
@Data
public class OmpBranchPushEntity {

	private String bbrq;

	private Integer branchId;

	private String ompBranchId;

	private String mid;

	@Override
	public boolean equals(Object object) {
		if (this == object)
			return true;
		if (object == null || getClass() != object.getClass())
			return false;
		OmpBranchPushEntity that = (OmpBranchPushEntity) object;
		return Objects.equals(bbrq, that.bbrq) && Objects.equals(branchId, that.branchId)
				&& Objects.equals(ompBranchId, that.ompBranchId) && Objects.equals(mid, that.mid);
	}

	@Override
	public int hashCode() {
		return Objects.hash(bbrq, branchId, ompBranchId, mid);
	}

}

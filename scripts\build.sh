#!/bin/bash

chmod +x ./mvnw

if [ 'master' = $CI_COMMIT_BRANCH ]; then
  img_name=registry.cn-hangzhou.aliyuncs.com/rlgj/tzxboh:boh-boot-v1
  docker build . -f Dockerfile -t $img_name
  docker push $img_name
fi

if [ 'dev' = $CI_COMMIT_BRANCH ]; then
  img_name=registry.cn-hangzhou.aliyuncs.com/rlgj/tzxboh:boh-boot-v1-dev
  docker build . -f Dockerfile -t $img_name
  docker push $img_name
fi


if [ 'develop' = $CI_COMMIT_BRANCH ]; then
  img_name=registry.cn-hangzhou.aliyuncs.com/rlgj/tzxboh:boh-boot-v1.1-dev
  docker build . -f Dockerfile -t $img_name
  docker push $img_name
fi


if [ 'base_img' = $CI_COMMIT_BRANCH ]; then
  img_name=registry.cn-hangzhou.aliyuncs.com/rlgj/tzxboh:boh-boot-v1.1-base
  docker build . -f base_img/Dockerfile -t $img_name
  docker push $img_name
fi


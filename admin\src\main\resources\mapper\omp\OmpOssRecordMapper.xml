<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.admin.omp.mapper.OmpOssRecordMapper">

	<insert id="batchInsertOssRecord" parameterType="com.tzx.admin.omp.entity.OmpOssRecordEntity">
		insert into tzxerp.erp_omp_oss (id,type,url,create_time) values
		<foreach collection="list" open="(" separator="," close=")" item="item">
			nextval('TZXERP.erp_omp_oss_seq'),#{item.type,jdbcType=VARCHAR},#{item.url,jdbcType=VARCHAR},now()
		</foreach>
 	</insert>
</mapper>
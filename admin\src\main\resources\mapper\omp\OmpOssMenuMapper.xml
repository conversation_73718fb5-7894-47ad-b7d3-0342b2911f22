<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.admin.omp.mapper.OmpOssMenuMapper">

	<select id="selectUnit" resultType="com.tzx.admin.omp.entity.OmpOssMenuUnit">
		select id dnid,mc dnname,id::VARCHAR dnno,2 dntype,id "order",0 status from TZXERP.ERP_UNITS
		UNION
		select id dnid,specitemname dnname,id::VARCHAR||specitemid::VARCHAR dnno ,2 dntype,id "order",0 status from vst_iteminfo_spec
	</select>

	<select id="selectCategory" resultType="com.tzx.admin.omp.entity.OmpOssMenuCategory">
		SELECT o.ompbranchid brandid,
			   A.xssx        "order",
			   1             packflag,
			   <PERSON><PERSON>          rcid,
			   <PERSON><PERSON>C1     rcname,
			   <PERSON>.<PERSON>        rcno,
			   0             status,
			   e.sjid        fatherid
		FROM (SELECT ed.ompid  AS ompBranchId,
					 max(JGXH) AS shopId
			  FROM TZXERP.ERP_ORGINFO eo
					   LEFT JOIN tzxerp.erp_orgbrand ed ON eo.ppsx = ed.ID
			  WHERE COALESCE(eo.ompid, '') != ''
				AND COALESCE(ed.ompid, '') != ''
			  GROUP BY ed.ompid) o
				 LEFT JOIN tzxerp.vst_cpyssz_three A ON o.shopId = A.jgxh
				 LEFT JOIN tzxerp.erp_orgsorts e on e.id = a.id
		WHERE A.channel = 'TS01'
		AND a.id != -1::numeric
		ORDER BY brandid, "order"
	</select>

	<select id="selectCategoryOne" resultType="com.tzx.admin.omp.entity.OmpOssMenuCategory">
		select A.id   "order",
			   1      packflag,
			   A.ID   rcid,
			   A.dlmc rcname,
			   A.dlbh rcno,
			   0      status,
			   0      fatherid
		from tzxerp.VST_ORGSORTS_DL a
	</select>

	<select id="selectPractice" resultType="com.tzx.admin.omp.entity.OmpOssMenuPractice">
		select '' mnemonic,id practiceid,nr practicename,bh practiceno,0 status,sort "order"  from tzxerp.vst_taste
	</select>

	<select id="selectDish" resultType="com.tzx.admin.omp.entity.OmpOssMenuDish">
		SELECT ii.xmmc  dishesname,
			   ii.xmbh  dishesno,
			   CASE

				   WHEN ii.xmsx = 'ERP_ITEM_PROPERTY_SINGLE' THEN
					   0
				   ELSE 1
				   END  packflag,
			   ''       dishesnamee,
			   CASE

				   WHEN COALESCE(ii.sfcz, 'N') = 'N' THEN
					   0
				   ELSE 1
				   END  isnoweighing,
			   0        onlypackflag,
			   0        isautoloadingpeople,
			   1        istartnum,
			   ii.pydm  mnemonic,
			   0        changnameflag,
			   ii.ID    "order",
			   ''       dishPadPicurl,
			   0        isstatisticspepoledishes,
			   1        issuitdcb,
			   ii.ID    dishesid,
			   0        isautoloadingtable,
			   ''       takeoutPicUrl,
			   ''       picture,
			   0        lengthtime,
			   CASE

				   WHEN if_changeable_price = 'Y' THEN
					   1
				   ELSE 0
				   END  changpriceflag,
			   1        isjoinclick,
			   ii.lbid  rcid,
			   0        sumnum,
			   0        sauceFlag,
			   CASE

				   WHEN ii.sfyx = 'Y' THEN
					   0
				   ELSE 1
				   END  status,
			   --ed.ompid brandid,
			   ii.dwid
		FROM tzxerp.erp_iteminfo ii
		WHERE ii.sfyx = 'Y'
	 	--LEFT JOIN TZXERP.ERP_ORGBRAND ed ON ii.ppsx = ed.ID :: TEXT
		--where COALESCE(ii.ppsx,'') !=''
		--and COALESCE(ed.ompid,'')!=''
	</select>

	<select id="selectMenu" resultType="com.tzx.admin.omp.entity.OmpOssMenu">
		SELECT DISTINCT ID
								menuid,
						cpysbh menuno,
						cpysmc1 menuname,
						0 status,
						ksrq menustarttime,
						ID "order",
						0 takeoutfoodflag,
						b.brandid
		FROM
			tzxerp.VST_CPYSSZ_ONE
				C LEFT JOIN (
				SELECT
					ed.ompid AS brandid,
					JGXH
				FROM
					TZXERP.ERP_ORGINFO eo
						LEFT JOIN tzxerp.erp_orgbrand ed ON eo.ppsx = ed.ID
			) b ON C.jgxh = b.jgxh
		WHERE
			COALESCE ( b.brandid, '' ) != ''
		ORDER BY b.brandid
	</select>

	<select id="selectSpec" resultType="com.tzx.admin.omp.entity.ErpItemSpec">
		select itemid item_id,id spec_id from tzxerp.vst_iteminfo_spec
	</select>
	
	<select id="selectPackageDetail" resultType="com.tzx.admin.omp.entity.ErpPackageDetail">
		SELECT mx.xmid                    pid,
			   CASE
				   WHEN mx.mxlx = 'ERP_MXLX_SINGLE'
					   THEN C.xmid
				   WHEN mx.mxlx = 'ERP_MXLX_GROUP'
					   THEN fz.ID END     cid,
			   CASE
				   WHEN mx.mxlx = 'ERP_MXLX_SINGLE' THEN C.cmmc
				   WHEN mx.mxlx = 'ERP_MXLX_GROUP'
					   THEN fz.igname END cmmc,
			   mx.cmsl,
			   mx.mxlx,
			   mx.xcsl                    max_count,
			   0                          is_default,
			   c.dwbh,
			   1                          mxtype
		FROM tzxerp.VST_ORGSILIST mx
				 LEFT JOIN tzxerp.VST_ITEMGROUP fz ON mx.mxxmid = fz.ID
				 LEFT JOIN tzxerp.VST_ITEMINFO C ON mx.mxxmid = C.xmid
		WHERE mxlx IN ('ERP_MXLX_SINGLE', 'ERP_MXLX_GROUP')
		  AND C.xmid IS NOT NULL
		UNION
		SELECT fz.ID                                  pid,
			   C.xmid                                 cid,
			   C.cmmc                                 cmmc,
			   fmx.fzsl                               cmsl,
			   'ERP_MXLX_GROUP_SINGLE'                mxlx,
			   fzsl                                   max_count,
			   CASE WHEN sfmr = 'Y' THEN 1 ELSE 0 END is_default,
			   c.dwbh,
			   2                                      mxtype
		FROM tzxerp.VST_ITEMGROUPLIST fmx
				 LEFT JOIN tzxerp.VST_ITEMGROUP fz ON fmx.igid = fz.ID
				 LEFT JOIN tzxerp.VST_ITEMINFO C ON fmx.xmid = C.xmid
		WHERE C.xmid IS NOT NULL
	</select>
	<select id ="selectMenudishespriceItem" resultType="com.tzx.admin.omp.entity.MenudishespriceItem">
		SELECT  p.id fdi_order,
				COALESCE ( hy, cmje ) fdm_memberprice,
				xmid fdi_dishesid,
				'' fdc_sellcycle,
				c.id fdi_menuid,
				TO_CHAR( now( ), 'YYYY-MM-DD HH24:MI:SS' ) fdd_updatetime,
				cmje fdm_price,
				o.ompBranchId brandid
		FROM
			vst_itempricesys P
				RIGHT JOIN (
				SELECT
					ed.ompid AS ompBranchId,
					MAX ( JGXH ) AS shopId
				FROM
					TZXERP.ERP_ORGINFO eo
						LEFT JOIN tzxerp.erp_orgbrand ed ON eo.ppsx = ed.ID
				WHERE
					COALESCE ( eo.ompid, '' ) != ''
				  AND COALESCE ( ed.ompid, '' ) != ''
				GROUP BY
					ed.ompid
			) o ON P.jgxh = o.shopid
				LEFT JOIN tzxerp.VST_CPYSSZ_ONE c on p.jgxh=c.jgxh
	</select>

</mapper>
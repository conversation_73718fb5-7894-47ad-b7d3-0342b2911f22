package com.tzx.admin.omp.service;

import org.junit.jupiter.api.Test;
import com.tzx.admin.omp.SyncBusinessDataService;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@SpringBootTest
public class TestSyncBusinessDataService {

	@Autowired
	SyncBusinessDataService syncBusinessDataService;

	@Test
	public void testSync() throws InterruptedException {
		syncBusinessDataService.sync("2024-04-01");
		TimeUnit.SECONDS.sleep(600L);
	}

	@Test
	public void testSyncByDoor() throws IOException {
		syncBusinessDataService.syncByDoor(1, "2021-01-01", null);
	}

}

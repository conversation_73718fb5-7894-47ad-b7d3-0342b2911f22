<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.admin.omp.mapper.ErpThirdInfoMapper">
	<resultMap id="BaseResultMap" type="com.tzx.admin.entity.ErpThirdInfo">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="thirdtype" property="thirdtype" jdbcType="VARCHAR" />
		<result column="thirdcode" property="thirdcode" jdbcType="VARCHAR" />
		<result column="thirdvalue" property="thirdvalue" jdbcType="VARCHAR" />
	</resultMap>
	<resultMap id="BaseVstResultMap" type="com.tzx.admin.entity.VstThirdInfo">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="thirdtype" property="thirdtype" jdbcType="VARCHAR" />
		<result column="thirdcode" property="thirdcode" jdbcType="VARCHAR" />
		<result column="thirdvalue" property="thirdvalue" jdbcType="VARCHAR" />
		<result column="jgxh" property="jgxh" jdbcType="INTEGER" />
	</resultMap>
	<select id="selectErpThirdInfoListByType" parameterType="com.tzx.admin.entity.ErpThirdInfo" resultMap="BaseResultMap">
		select ID, THIRDTYPE, THIRDCODE, THIRDVALUE
		from  TZXERP.ERP_THIRD_INFO
		<where>
			<if test="thirdtype != null and thirdtype != ''">
				thirdtype = #{thirdtype}
			</if>
			<if test="thirdcode != null and thirdcode != ''">
				and thirdcode = #{thirdcode}
			</if>
		</where>
	</select>



	<select id="selectVstThirdInfoListByType" resultMap="BaseVstResultMap" parameterType="com.tzx.admin.entity.VstThirdInfo">
		SELECT id,thirdtype,thirdcode,thirdvalue,jgxh FROM TZXERP.VST_THIRD_INFO
		<where>
			<if test="thirdtype != null and thirdtype != ''">
				thirdtype = #{thirdtype}
			</if>
			<if test="thirdcode != null and thirdcode != ''">
				and thirdcode = #{thirdcode}
			</if>
			<if test="jgxh != null">
				and jgxh = #{jgxh,jdbcType=INTEGER}
			</if>
		</where>
	</select>
</mapper>
name: 代码阅读
description: 帮助开发者阅读代码，定位和排查问题的任务流程
todos: []
steps:
- title: 收集问题信息
  worker: developer
  rule:
  - 询问当前遇到的具体问题是什么
  - 收集用户已了解到的项目信息，包括相关接口、数据库、类名、MQ队列名称等
  - 询问问题的表现形式、复现步骤、影响范围
  - 询问用户对问题原因的初步猜测
  - 与用户确认收集到的信息是否准确完整
  - 需要用户确认后才能进入下一步
  - 生成问题简要描述 -> problem_desc
  output:
  - doc: _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/problem_description.md
    template: '# 问题描述文档


      ## 问题概述

      - 问题现象：

      - 影响范围：

      - 复现步骤：


      ## 项目相关信息

      - 相关接口：

      - 相关数据库表：

      - 相关类名：

      - 相关MQ队列：

      - 其他关键信息：


      ## 初步分析

      - 可能的原因：

      - 需要重点关注的代码区域：

      '
- title: 代码阅读与分析
  worker: developer
  input:
  - doc: _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/problem_description.md
  rule:
  - 根据问题描述文档中的信息，查找并阅读相关代码
  - 分析代码的执行流程和逻辑结构
  - 重点关注可能导致问题的代码区域
  - 分析类之间的关系、方法调用链路和数据流向
  - 检查错误处理和边界条件处理
  - 对数据库操作和消息处理逻辑进行分析
  - 需要与用户确认分析结果是否符合预期
  output:
  - doc: _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/code_analysis.md
    template: "# 代码分析文档\n\n## 代码结构概览\n- 主要类和接口：\n- 核心业务流程：\n- 组件间交互：\n\n## 执行流程分析\n\
      ```\n[详细的方法调用链和执行流程图]\n```\n\n## 关键代码分析\n### 模块1: [模块名]\n```java\n// 关键代码片段和解释\n\
      ```\n\n### 模块2: [模块名]\n```java\n// 关键代码片段和解释\n```\n\n## 潜在问题点\n1. [问题点1]：\n\
      \   - 代码位置：\n   - 问题分析：\n   - 可能影响：\n\n2. [问题点2]：\n   - 代码位置：\n   - 问题分析：\n\
      \   - 可能影响：\n\n## 总结分析\n[对整体代码的评估和问题的最终定位]\n"
- title: 问题解决方案
  worker: developer
  input:
  - doc: _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/problem_description.md
  - doc: _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/code_analysis.md
  rule:
  - 询问用户是否需要修改代码来解决问题
  - 如果需要，提出具体的代码修改方案
  - 修改方案应遵循项目现有代码风格和架构
  - 提供修改前后的代码对比
  - 分析修改可能带来的影响和风险
  - 与用户确认修改方案
  output:
  - doc: _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/solution_proposal.md
    template: '# 问题解决方案


      ## 解决方案概述

      [简要描述解决方案]


      ## 具体修改

      ### 文件1: [文件路径]

      ```diff

      - // 原代码

      + // 新代码

      ```


      ### 文件2: [文件路径]

      ```diff

      - // 原代码

      + // 新代码

      ```


      ## 修改影响分析

      - 功能影响：

      - 性能影响：

      - 兼容性影响：

      - 可能的副作用：


      ## 验证方案

      - 如何验证修改解决了问题：

      - 建议的测试场景：

      '
current_todo_index: null
current_step_index: 2
memory: {}

id: java_dev
name: Java开发流程
description: 标准化的Java研发需求实现流程，包含从需求分析到上线的完整流程
# 工作流的规则
rules:
  - 分析要尽可能详细
  - 分析原生文件时必须阅读全部代码行
  - 生成的所有文档如果没有指定明确路径，则统一放在 {{work_path}} 目录下
  - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
  - 任务执行时，output内容必须遵照template模板格式
  - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
  - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务

# 工作流的任务列表
tasks:
  - name: 询问用户需求的TAPD需求链接, TAPDID, TAPD中需求文字内容, 以及图片,文件等. 
    description: 获取TAPD需求
    worker: pd
    rules:
      - 获取TAPDID 作为后续变量
      - 本阶段将获取到的TAPD信息, 整理问需求来源文档
    output:
      - doc: story_tapd_{{ TAPDID }}.md
        template: |
          # 需求来源TAPD
          TAPDID: {{TAPDID}}
          URL: 用户提供的TAPD需求链接

          # 需求描述 
          [用户提供的TAPD原始需求文字内容, 图片, 文件等]

          # 需求时间
          [用户提供的TAPD原始需求中的需求时间]

  - name: 需求内容分析
    description: 分析tapd需求内容, 进行需求概况分析
    worker: pd
    rules:
      - 需要获取完整的需求文档
      - 必须确认需求的边界和验收标准
      - 必须与需求提出者确认需求内容的准确性
    input: 
      - doc: story_tapd_{{ TAPDID }}.md
    output:
      - doc: story_analysis_{{ TAPDID }}.md
        template: |
          # 需求分析报告

          ## 需求概述
          [提供需求的简要描述, TAPD链接, TAPDID]

          ## 需求详情
          [详细描述需求内容，包括功能点列表]

          ## 需求边界
          [明确说明哪些内容在需求范围内，哪些不在]

          ## 技术可行性分析
          [分析需求的技术可行性，包括潜在挑战]

          ## 时间评估
          [预计完成需求所需的时间]

          ## 问题和风险
          [列出实现过程中可能遇到的问题和风险]

  - name: 技术方案设计
    description: 制定详细的技术实现方案与测试用例
    worker: developer
    rules:
      - 必须阅读相关代码，理解现有系统架构
      - 设计方案需要考虑系统扩展性和维护性
      - 测试用例必须覆盖主流程和边界场景
    input:
      - doc: story_analysis_{{ TAPDID }}.md
    output:
      - doc: story_tech_design_{{ TAPDID }}.md
        template: |
          # 技术设计文档

          ## 系统架构
          [描述系统架构，包括相关组件和交互]

          ## 类设计
          [提供主要类的设计，包括类图]

          ## 数据库设计
          [如有需要，提供数据库表结构设计]

          ## 接口设计
          [详细描述API接口设计，包括参数和返回值]

          ## 实现流程
          [描述主要功能的实现流程，可使用流程图]

          ## 技术选型
          [说明所使用的技术栈和依赖库]

          ## 安全考虑
          [描述安全相关的设计考虑]

      - doc: story_test_cases_{{ TAPDID }}.md
        template: |
          # 测试用例设计

          ## 单元测试用例
          [列出关键组件的单元测试用例]

          ## 集成测试用例
          [描述系统集成测试用例]

          ## 功能测试用例
          [列出功能测试的测试场景和预期结果]

          ## 性能测试用例
          [如有需要，提供性能测试的测试计划]

  - name: 开发任务拆分
    description: 将需求拆分为可执行的开发任务，并进行工作量估算
    worker: developer
    rules:
      - 任务拆分需要考虑依赖关系
      - 每个任务应该有明确的工作量估算
      - 任务应按优先级排序
    input:
      - doc: story_analysis_{{ TAPDID }}.md
      - doc: story_tech_design_{{ TAPDID }}.md
    output:
      - doc: story_development_tasks_{{ TAPDID }}.md
        template: |
          # 开发任务拆分

          ## 任务列表
          | ID | 任务名称 | 描述 | 优先级 | 工作量(人天) | 负责人 | 依赖任务 |
          | -- | ------- | ---- | ----- | ----------- | ----- | ------- |
          | 1  | [任务名] | [描述] | [高/中/低] | [估算] | [人员] | [依赖任务ID] |

          ## 开发时间线
          [提供开发时间线，包括关键里程碑]

          ## 资源需求
          [描述完成任务所需的资源]

  - name: 代码开发实现
    description: 按照技术方案完成代码开发，保证代码质量
    worker: developer
    rules:
      - 必须遵循代码规范
      - 必须编写单元测试
      - 必须进行代码自检
    input:
      - doc: story_analysis_{{ TAPDID }}.md
      - doc: story_tech_design_{{ TAPDID }}.md
      - doc: story_test_cases_{{ TAPDID }}.md
    output:
      - doc: story_development_report_{{ TAPDID }}.md
        template: |
          # 开发实现报告

          ## 已完成功能
          [列出已完成的功能点]

          ## 代码结构
          [描述代码的主要结构和组织方式]

          ## 单元测试覆盖率
          [提供单元测试覆盖率报告]

          ## 注意事项
          [列出使用或修改代码时需要注意的事项]

          ## 未解决问题
          [列出尚未解决的问题或限制]

  - name: 代码测试与验证
    description: 验证代码功能是否符合需求，确保系统稳定性
    worker: tester
    rules:
      - 必须执行所有测试用例
      - 必须记录所有发现的问题
      - 必须验证修复的bug
    input:
      - doc: story_tech_design_{{ TAPDID }}.md
      - doc: story_test_cases_{{ TAPDID }}.md
      - doc: story_analysis_{{ TAPDID }}.md
    output:
      - doc: story_test_report_{{ TAPDID }}.md
        template: |
          # 测试报告

          ## 测试概述
          [提供测试的总体情况]

          ## 测试覆盖率
          [描述测试覆盖的功能点和场景]

          ## 发现的问题
          | ID | 问题描述 | 严重程度 | 状态 | 修复人 | 验证结果 |
          | -- | ------- | ------- | ---- | ----- | ------- |
          | 1  | [描述]  | [高/中/低] | [开放/修复中/已修复/已验证] | [人员] | [通过/未通过] |

          ## 性能测试结果
          [如有进行性能测试，提供测试结果]

          ## 测试结论
          [给出测试的整体结论]

  - name: API文档生成与发布
    description: 提供完整准确的API文档，确保前后端接口一致性
    worker: developer
    rules:
      - API文档必须与实际实现保持一致
      - 必须提供接口调用示例
      - 必须说明所有参数和返回值
    input:
      - doc: story_tech_design_{{ TAPDID }}.md
    output:
      - doc: story_api_documentation_{{ TAPDID }}.md
        template: |
          # API文档

          ## API概述
          [提供API的总体描述]

          ## 接口列表
          [按模块列出所有接口]

          ## 接口详情
          [对每个接口提供详细说明]

          ### 接口名称
          - URL: [接口URL]
          - 方法: [GET/POST/PUT/DELETE]
          - 描述: [接口功能描述]
          - 请求参数:
            ```json
            {
              "参数名": "类型 - 描述 - 是否必须"
            }
            ```
          - 返回结果:
            ```json
            {
              "状态码": "描述",
              "数据结构": {
                "字段名": "类型 - 描述"
              }
            }
            ```
          - 调用示例:
            ```java
            // 调用示例代码
            ```
          - 注意事项: [使用该接口时需要注意的事项]

  - name: 前后端联调
    description: 确保前后端接口正常对接，完成功能集成
    worker: developer
    rules:
      - 必须与前端开发人员密切配合
      - 必须记录联调过程中发现的问题
      - 必须验证所有功能点
    input:
      - doc: story_api_documentation_{{ TAPDID }}.md
      - doc: story_test_cases_{{ TAPDID }}.md
    output:
      - doc: story_integration_report_{{ TAPDID }}.md
        template: |
          # 前后端联调报告

          ## 联调概述
          [描述联调的范围和目标]

          ## 联调过程
          [记录联调的主要步骤和发现]

          ## 问题记录
          | ID | 问题描述 | 解决方案 | 状态 |
          | -- | ------- | ------- | ---- |
          | 1  | [描述]  | [解决方案] | [已解决/未解决] |

          ## 联调结果
          [描述联调的最终结果]

          ## 下一步计划
          [如有未解决问题，提供下一步计划]

  - name: Bug修复与优化
    description: 解决测试和联调中发现的问题，优化代码质量和性能
    worker: developer
    rules:
      - 必须理解问题根本原因
      - 修复必须经过验证
      - 必须保证不引入新的问题
    input:
      - doc: story_test_report_{{ TAPDID }}.md
      - doc: story_integration_report_{{ TAPDID }}.md
    output:
      - doc: story_bugfix_report_{{ TAPDID }}.md
        template: |
          # Bug修复报告

          ## 修复概述
          [描述修复的范围和方法]

          ## 修复记录
          | ID | 问题描述 | 根本原因 | 修复方法 | 验证结果 |
          | -- | ------- | ------- | ------- | ------- |
          | 1  | [描述]  | [原因]   | [方法]   | [结果]   |

          ## 代码优化
          [描述进行的代码优化工作]

          ## 性能改进
          [描述性能改进的措施和效果]

          ## 遗留问题
          [列出尚未解决的问题]

  - name: 需求总结与上线准备
    description: 总结开发过程和经验，准备系统上线
    worker: planner
    rules:
      - 必须编写完整的上线文档
      - 必须制定回滚方案
      - 必须总结项目经验和教训
    input:
      - doc: story_requirement_analysis_{{ TAPDID }}.md
      - doc: story_tech_design_{{ TAPDID }}.md
      - doc: story_development_report_{{ TAPDID }}.md
      - doc: story_test_report_{{ TAPDID }}.md
      - doc: story_bugfix_report_{{ TAPDID }}.md
    output:
      - doc: story_deployment_plan_{{ TAPDID }}.md
        template: |
          # 上线计划

          ## 上线流程
          [详细描述上线步骤]

          ## 上线时间表
          [提供上线时间计划]

          ## 环境需求
          [描述上线所需的环境配置]

          ## 回滚方案
          [提供详细的回滚策略]

          ## 上线风险
          [分析上线可能的风险和应对措施]

      - doc: story_summary_{{ TAPDID }}.md
        template: |
          # 项目总结

          ## 项目概述
          [简要描述项目内容和目标]

          ## 主要成果
          [列出项目的主要成果]

          ## 技术亮点
          [描述项目中的技术亮点]

          ## 经验教训
          [总结项目过程中的经验和教训]

          ## 后续改进
          [提出后续可能的改进方向]

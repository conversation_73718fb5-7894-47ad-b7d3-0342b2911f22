package com.tzx.admin.omp.event;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;

import java.util.HashSet;
import java.util.concurrent.TimeUnit;

@SpringBootTest
public class TestOmpPushEventListener {

	@Autowired
	ApplicationEventPublisher eventPublisher;

	@Test
	void testShopCallBackEvent() throws InterruptedException {
		eventPublisher.publishEvent(new ShopCallBackEvent(this, 1, "2020-01-01", "ossPath", null, null));
		System.out.println(3333);
		TimeUnit.SECONDS.sleep(30L);
	}

	@Test
	void testCheckAllPushEvent() throws InterruptedException {
		eventPublisher.publishEvent(new CheckAllPushEvent(this, new HashSet<>()));
		System.out.println(3333);
		TimeUnit.SECONDS.sleep(30L);
	}

}

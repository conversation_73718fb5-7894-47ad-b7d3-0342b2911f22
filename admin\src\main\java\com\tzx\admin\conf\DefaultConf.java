package com.tzx.admin.conf;

import com.hccake.ballcat.common.oss.DefaultOssTemplate;
import com.hccake.ballcat.common.oss.OssProperties;
import com.hccake.ballcat.common.oss.OssTemplate;
import com.tzx.admin.entity.ErpThirdInfo;
import com.tzx.admin.omp.mapper.ErpThirdInfoMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.tzx.admin.constants.ErpThirdTypeConstant.*;

@Configuration
@ConditionalOnProperty(name = "omp.enable", havingValue = "false")
public class DefaultConf {

	/**
	 * 后续可以改为从数据读取 通用化 从配置文件获取 或者从db获取
	 * @param erpThirdInfoMapper
	 * @return
	 */
	@Bean
	public OssTemplate profileOmpOssTemplate(ErpThirdInfoMapper erpThirdInfoMapper) {
		ErpThirdInfo erpThirdInfo = new ErpThirdInfo();
		erpThirdInfo.setThirdtype(OMP_OSS_TYPE);
		List<ErpThirdInfo> erpThirdInfos = erpThirdInfoMapper.selectErpThirdInfoListByType(erpThirdInfo);
		Map<String, String> collect = erpThirdInfos.stream()
			.collect(Collectors.toMap(ErpThirdInfo::getThirdcode, ErpThirdInfo::getThirdvalue, (a, b) -> b));
		OssProperties ossProperties = genOssProperties(collect);
		ossProperties.setPathStyleAccess(true);
		ossProperties.setEndpoint(collect.get(PROFILES_OMP_ENDPOINT));
		ossProperties.setBucket(collect.get(OMP_PROFILE_BUCKET_NAME));
		return new DefaultOssTemplate(ossProperties);
	}

	private OssProperties genOssProperties(Map<String, String> ompOssBucket) {
		String accessKey = ompOssBucket.get(ACCESS_KEY_ID);
		String endPoint = ompOssBucket.get(ENDPOINT);
		String accessSecret = ompOssBucket.get(ACCESS_KEY_SECRET);
		OssProperties ossProperties = new OssProperties();
		ossProperties.setPathStyleAccess(true);
		ossProperties.setAccessSecret(accessSecret);
		ossProperties.setAccessKey(accessKey);
		ossProperties.setEndpoint(endPoint);
		ossProperties.setRegion("cn-beijing");
		return ossProperties;
	}

}

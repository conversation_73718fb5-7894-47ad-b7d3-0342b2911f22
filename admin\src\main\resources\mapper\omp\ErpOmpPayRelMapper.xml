<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.admin.omp.mapper.ErpOmpPayRelMapper">
	<resultMap id="BaseResultMap" type="com.tzx.admin.omp.entity.ErpOmpPayRel">
		<id column="omp_id" property="ompId" jdbcType="BIGINT" />
		<result column="tzx_pay_id" property="tzxPayId" jdbcType="VARCHAR" />
		<result column="sync_status" property="syncStatus" jdbcType="VARCHAR" />
	</resultMap>
	<resultMap id="TzxPaymentMap" type="com.tzx.admin.omp.entity.ErpPayment">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="ylc" property="ylc" jdbcType="VARCHAR" />
		<result column="fkfsbh" property="fkfsbh" jdbcType="VARCHAR" />
		<result column="fkfsmc" property="fkfsmc" jdbcType="VARCHAR" />
		<result column="sfkfp" property="sfkfp" jdbcType="VARCHAR" />
		<result column="fklxid" property="fklxid" jdbcType="VARCHAR" />
		<result column="fklxmc" property="fklxmc" jdbcType="VARCHAR" />
		<result column="pxbh" property="pxbh" jdbcType="NUMERIC" />
		<result column="sfyx" property="sfyx" jdbcType="VARCHAR" />
	</resultMap>

	<sql id="Base_Column_List">
		omp_id, tzx_pay_id, sync_status
	</sql>

	<select id="selectErpPaymentList" resultMap="TzxPaymentMap" >
		select t.id,t.ylc,p1.simplified as ylcmc,t.fkfsmc,p.simplified as fklxmc,g.FZBZMC fkfsfzmc,t.*
		from tzxerp.erp_payments t
				 left join TZXERP.ERP_PAYMENTSGROUP g on t.FKFSFZID = g.ID
				 left join (SELECT LAN.ID, LAN.SIMPLIFIED, DIC.TYPE_NAME, DIC.TYPE_CODE, DIC.ITEM_CODE
							FROM TZXPL.PLATFORM_TYPE_DIC DIC
									 JOIN TZXPL.PLATFORM_LANGUAGE LAN
										  ON DIC.ITEM_NAME = LAN.ID
							where type_code = 'ERP_PAYMENTS_TYPE' ) p on t.fklxid = p.ITEM_CODE
				 left join (SELECT LAN.ID, LAN.SIMPLIFIED, DIC.TYPE_NAME, DIC.TYPE_CODE, DIC.ITEM_CODE
							FROM TZXPL.PLATFORM_TYPE_DIC DIC
									 JOIN TZXPL.PLATFORM_LANGUAGE LAN
										  ON DIC.ITEM_NAME = LAN.ID
							where type_code = 'ERP_FKFS' ) p1 on t.ylc = p1.ITEM_CODE;
	</select>

	<select id="selectOrgOmpIdListByPayId" resultType="java.lang.String" >
		SELECT
			t2.ompid
		FROM
			tzxerp.erp_payments t
		LEFT JOIN tzxerp.erp_payment_org_ref t1 ON t.ID = t1.fkfsid
		LEFT JOIN tzxerp.erp_orginfo t2 ON t1.jgid = t2.ID
		WHERE
			t.ID = #{payId}
		AND t2.ompid IS NOT NULL;
	</select>

	<select id="selectToSyncWelifePaymentList" resultMap="TzxPaymentMap">
		select t.id,t.ylc,p1.simplified as ylcmc,t.fkfsmc,p.simplified as fklxmc,g.FZBZMC fkfsfzmc,t.*
		from tzxerp.erp_payments t
			 left join TZXERP.ERP_PAYMENTSGROUP g on t.FKFSFZID = g.ID
			 left join (SELECT LAN.ID, LAN.SIMPLIFIED, DIC.TYPE_NAME, DIC.TYPE_CODE, DIC.ITEM_CODE
						FROM TZXPL.PLATFORM_TYPE_DIC DIC
								 JOIN TZXPL.PLATFORM_LANGUAGE LAN
									  ON DIC.ITEM_NAME = LAN.ID
						where type_code = 'ERP_PAYMENTS_TYPE' ) p on t.fklxid = p.ITEM_CODE
			 left join (SELECT LAN.ID, LAN.SIMPLIFIED, DIC.TYPE_NAME, DIC.TYPE_CODE, DIC.ITEM_CODE
						FROM TZXPL.PLATFORM_TYPE_DIC DIC
								 JOIN TZXPL.PLATFORM_LANGUAGE LAN
									  ON DIC.ITEM_NAME = LAN.ID
						where type_code = 'ERP_FKFS' ) p1 on t.ylc = p1.ITEM_CODE
		where not exists (
		    select 1 from tzxerp.erp_welife_pay_rel a where a.tzx_pay_id = t.id
		);
	</select>

	<insert id="saveWelifePayRef" parameterType="com.tzx.admin.omp.entity.ErpWelifePayRel">
		insert into tzxerp.erp_welife_pay_rel(welife_id,tzx_pay_id,created_at,updated_at)
		values (#{welifeId}, #{tzxPayId} ,now(),now());
	</insert>


</mapper>
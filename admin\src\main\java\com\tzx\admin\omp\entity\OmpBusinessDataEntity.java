package com.tzx.admin.omp.entity;

import lombok.Data;

@Data
public class OmpBusinessDataEntity {

	private String businessDate;

	private String orderAmount;

	private String orderChannel;

	private String orderCount;

	private String pmid;

	private String realAmount;

	private String platformPaymentAmount;

	public static OmpBusinessDataEntity genOmpEntityByDisCount(ErpDisCountData erpDisCountData) {
		OmpBusinessDataEntity ompBusinessDataEntity = new OmpBusinessDataEntity();
		ompBusinessDataEntity.setOrderCount("0");
		ompBusinessDataEntity.setOrderChannel("4");
		ompBusinessDataEntity.setPmid("-1");
		ompBusinessDataEntity.setRealAmount("0");
		ompBusinessDataEntity.setPlatformPaymentAmount("0");
		ompBusinessDataEntity.setBusinessDate(erpDisCountData.getBbrq());
		ompBusinessDataEntity.setOrderAmount(erpDisCountData.getMoney().toString());
		return ompBusinessDataEntity;
	}

	public static OmpBusinessDataEntity genOmpEntityByPayment(ErpPaymentData erpPaymentData) {
		OmpBusinessDataEntity ompBusinessDataEntity = new OmpBusinessDataEntity();
		ompBusinessDataEntity.setBusinessDate(erpPaymentData.getBbrq());
		ompBusinessDataEntity.setOrderCount(String.valueOf(erpPaymentData.getCount()));
		ompBusinessDataEntity.setRealAmount(erpPaymentData.getFkje().toString());
		ompBusinessDataEntity.setOrderAmount(erpPaymentData.getFkje().toString());
		ompBusinessDataEntity.setPmid(String.valueOf(erpPaymentData.getPmid()));
		ompBusinessDataEntity.setOrderChannel(erpPaymentData.getSource());
		return ompBusinessDataEntity;
	}

}

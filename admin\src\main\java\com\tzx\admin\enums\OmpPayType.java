package com.tzx.admin.enums;

/**
 * OmpPayType
 *
 * ERP_PAYMENTS_TICKET 票券类 4 ERP_PAYMENTS_DISBILL 免单类 10 ERP_PAYMENTS_CREDIT 信用卡类 7
 * ERP_PAYMENTS_TRANSFER 转账类 9 ERP_PAYMENTS_CHECK 支票类 9 ERP_PAYMENTS_MEMCARD 会员卡类 2
 * ERP_PAYMENTS_OTHER 第三方付款类 3 ERP_PAYMENTS_CASH 现金类 1 ERP_PAYMENTS_PUTACCOUNT 挂账类 6
 * ERP_PAYMENTS_CARD 第三方卡支付 7 ERP_PAYMENTS_DSFGZ 第三方挂账 6 FKSX_SAS SAAS会员卡 2
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-26
 */
public enum OmpPayType {

	ERP_PAYMENTS_TICKET("ERP_PAYMENTS_TICKET", 4), ERP_PAYMENTS_DISBILL("ERP_PAYMENTS_DISBILL", 10),
	ERP_PAYMENTS_CREDIT("ERP_PAYMENTS_CREDIT", 7), ERP_PAYMENTS_TRANSFER("ERP_PAYMENTS_TRANSFER", 9),
	ERP_PAYMENTS_CHECK("ERP_PAYMENTS_CHECK", 9), ERP_PAYMENTS_MEMCARD("ERP_PAYMENTS_MEMCARD", 2),
	ERP_PAYMENTS_OTHER("ERP_PAYMENTS_OTHER", 3), ERP_PAYMENTS_CASH("ERP_PAYMENTS_CASH", 1),
	ERP_PAYMENTS_PUTACCOUNT("ERP_PAYMENTS_PUTACCOUNT", 6), ERP_PAYMENTS_CARD("ERP_PAYMENTS_CARD", 7),
	ERP_PAYMENTS_DSFGZ("ERP_PAYMENTS_DSFGZ", 6), FKSX_SAS("FKSX_SAS", 2);

	private String bohPayTypeName;

	private Integer ompPmType;

	OmpPayType(String bohPayTypeName, Integer ompPmType) {
		this.bohPayTypeName = bohPayTypeName;
		this.ompPmType = ompPmType;
	}

	public String getBohPayTypeName() {
		return bohPayTypeName;
	}

	public void setBohPayTypeName(String bohPayTypeName) {
		this.bohPayTypeName = bohPayTypeName;
	}

	public Integer getOmpPmType() {
		return ompPmType;
	}

	public void setOmpPmType(Integer ompPmType) {
		this.ompPmType = ompPmType;
	}

	/**
	 * 根据bohPayTypeName获取ompPmType
	 */
	public static Integer getOmpPmType(String bohPayTypeName) {
		for (OmpPayType ompPayType : OmpPayType.values()) {
			if (ompPayType.getBohPayTypeName().equals(bohPayTypeName)) {
				return ompPayType.getOmpPmType();
			}
		}
		return null;
	}

}

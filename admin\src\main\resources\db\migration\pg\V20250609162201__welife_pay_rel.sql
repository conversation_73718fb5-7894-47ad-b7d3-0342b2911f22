create table if not exists tzxerp.erp_welife_pay_rel(
    welife_id bigint not null  primary key,
    tzx_pay_id bigint not null,
    created_at timestamp without time zone not null,
    updated_at timestamp without time zone not null,
    deleted_at timestamp without time zone
);
comment on table tzxerp.erp_welife_pay_rel is 'welife支付关系表';
comment on column tzxerp.erp_welife_pay_rel.welife_id is '同步到微生活后, 在微生活中的ID';
comment on column tzxerp.erp_welife_pay_rel.tzx_pay_id is '天子星支付方式id';
comment on column tzxerp.erp_welife_pay_rel.created_at is '创建时间';
comment on column tzxerp.erp_welife_pay_rel.updated_at is '更新时间';
comment on column tzxerp.erp_welife_pay_rel.deleted_at is '删除时间';
-- 唯一索引  tzx_pay_id
create unique index if not exists welife_pay_rel_tzx_pay_id_uk on tzxerp.erp_welife_pay_rel(tzx_pay_id);

package com.tzx.admin.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public final class DateUtils {

	private DateUtils() {
	}

	/**
	 * 获取当前日期几天前的日期
	 * @param day 前几天
	 * @param format 日期格式
	 * @return
	 */

	public static List<String> getTimesAgo(Integer day, String format) {
		List<String> result = new ArrayList<>();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

		// 获取当前日期
		LocalDate currentDate = LocalDate.now();

		// 减去两天
		LocalDate someDaysAgo = currentDate.minusDays(day);

		while (someDaysAgo.isBefore(currentDate)) {
			result.add(someDaysAgo.format(formatter));
			someDaysAgo = someDaysAgo.plusDays(1);
		}

		return result;
	}

}

package com.tzx.admin.omp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzx.admin.omp.entity.ErpOmpPosBillEntity;
import com.tzx.admin.omp.entity.OmpBranchPushEntity;

import java.util.List;

public interface ErpOmpPosBillMapper extends BaseMapper<ErpOmpPosBillEntity> {

	ErpOmpPosBillEntity selectByErpOmpPosBillEntity(ErpOmpPosBillEntity erpOmpPosBillEntity);

	void InsertOmpPosBill(ErpOmpPosBillEntity erpOmpPosBillEntity);

	void updatePosBill(ErpOmpPosBillEntity erpOmpPosBillEntity);

	List<Integer> checkNoDayEnd(OmpBranchPushEntity ompBranchPushEntity);

	Integer countFrom(OmpBranchPushEntity ompBranchPushEntity);

}

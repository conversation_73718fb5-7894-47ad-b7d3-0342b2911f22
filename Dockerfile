FROM registry.cn-hangzhou.aliyuncs.com/rlgj/tzxboh:boh-boot-v1.1-base

COPY . /build/

RUN cd /build && chmod +x ./mvnw && ./mvnw spring-javaformat:apply && ./mvnw clean package -DskipTests
RUN cp /build/scripts/start.sh /start.sh
RUN chmod +x /start.sh && mkdir /app/
RUN cp /build/scripts/chinesemsyh.ttf /usr/share/fonts/truetype/myfont/chinesemsyh.ttf
RUN fc-cache -f -v && cp /build/admin/target/admin*.jar /app/app.jar

ENV TZ=Asia/Shanghai
ENV APP_PORT=8080
WORKDIR /app

EXPOSE $APP_PORT

CMD  ["bash", "/start.sh"]
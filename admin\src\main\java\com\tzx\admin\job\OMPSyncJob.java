package com.tzx.admin.job;

import com.tzx.admin.omp.SyncBusinessDataService;
import com.tzx.admin.omp.SyncPayToOmpService;
import com.tzx.admin.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;

import static com.tzx.admin.constants.OmpConstant.FORMAT_DATE_YYYY_MM_DD;

@Slf4j
public class OMPSyncJob {

	@Value("${day:2}")
	private Integer day;

	private final SyncPayToOmpService syncPayToOmpService;

	final SyncBusinessDataService syncBusinessDataService;

	public OMPSyncJob(SyncPayToOmpService syncPayToOmpService, SyncBusinessDataService syncBusinessDataService) {
		this.syncPayToOmpService = syncPayToOmpService;
		this.syncBusinessDataService = syncBusinessDataService;
	}

	@Scheduled(cron = "0 0 0 * * ?")
	// @Scheduled(cron="*/10 * * * *")
	public void sync() {
		synchronized (OMPSyncJob.class) {
			log.info("do omp sync");
			syncPayToOmpService.sync();
			log.info("do welife sync");
			syncPayToOmpService.syncWelifePay();
		}
	}

	@Scheduled(cron = "0 0,30 0,1,2,3,4,18,19,20,21,22,23 * * ? ")
	public void ompBillDataSync() {
		log.info("do omp  BillData sync");
		List<String> timesAgo = DateUtils.getTimesAgo(2, FORMAT_DATE_YYYY_MM_DD);
		for (String ago : timesAgo) {
			syncBusinessDataService.sync(ago);
		}

	}

}

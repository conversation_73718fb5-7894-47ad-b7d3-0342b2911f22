<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzx.admin.omp.mapper.OmpBohIdRefMapper">

	<cache/>

	<select id="findByNameAndBrandIdAndBohId" resultType="com.tzx.admin.omp.entity.OmpBohIdRef">
		SELECT *
		FROM tzxerp.omp_boh_id_ref
		WHERE name = #{name}
		  AND brand_id = #{brandId}
		  AND boh_id = #{bohId}
	</select>

	<select id="findMaxOmpIdByName" resultType="java.lang.Integer" useCache="false">
		SELECT MAX(omp_id) FROM tzxerp.omp_boh_id_ref WHERE name IN
		<foreach collection='name' item='item' open='(' separator=',' close=')'>
			#{item}
		</foreach>
	</select>

	<insert id="insert">
		INSERT INTO tzxerp.omp_boh_id_ref(name, brand_id, boh_id, omp_id)
		VALUES (#{name}, #{brandId}, #{bohId}, #{ompId})
	</insert>
</mapper>
package com.tzx.admin.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 对接微生活接口
 */
public class WSHUtils {

	public static String getSin(JSONObject jsonParam, Long time, Map thirdData) {
		String appid = thirdData.get("APPID").toString();
		String appkey = thirdData.get("APPKEY").toString();
		String version = thirdData.get("VERSION").toString();
		/* ==============自定义序列化json，根据key和value判断是否序列化============= */
		/*
		 * PropertyFilter filter = new PropertyFilter() { public boolean apply(Object
		 * source, String name, Object value){ if
		 * ("organ_id".equals(name)||null==value||value.equals("")||value.toString().
		 * equals("{}")||((value instanceof Object[]) &&((Object[])value).length==0)) {
		 * return false; } System.out.println(name+":"+value); return true; } };
		 */
		JSONObject jsonParent = new JSONObject();
		jsonInput(jsonParent, null, jsonParam);
		StringBuilder sb = new StringBuilder();

		ArrayList<String> list = new ArrayList<String>();
		for (Map.Entry<String, Object> entry : jsonParent.entrySet()) {
			list.add(entry.getKey() + "=" + entry.getValue() + "&");
		}

		int size = list.size();
		String[] arrayToSort = list.toArray(new String[size]);
		Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
		for (int i = 0; i < size; i++) {
			sb.append(arrayToSort[i]);
		}

		sb.append("appid=")
			.append(appid)
			.append("&appkey=")
			.append(appkey)
			.append("&v=")
			.append(version)
			.append("&ts=")
			.append(time.toString());
		System.out.println("加密前：" + sb.toString());
		return DigestUtils.md5Hex(sb.toString());
	}

	/**
	 * 深度遍历json参数，并去掉空置，组装成一个新的jsonParent
	 * @param jsonParent
	 * @param name
	 * @param json
	 */
	private static void jsonInput(JSONObject jsonParent, String name, JSONObject json) {
		if (null == json || json.isEmpty()) {
			return;
		}
		for (Map.Entry<String, Object> entry : json.entrySet()) {
			String keyName = entry.getKey();
			if (null != name && !"".equals(name)) {
				keyName = name + "@@[" + entry.getKey() + "]";
			}
			if (entry.getValue() instanceof JSONArray) {
				jsonArrayInput(jsonParent, keyName, (JSONArray) entry.getValue());
			}
			else if (entry.getValue() instanceof JSONObject) {
				jsonInput(jsonParent, keyName, (JSONObject) entry.getValue());
			}
			else if (null != entry
				.getValue()/* &&entry.getValue().toString().length()>0 */) {
				jsonParent.put(keyName, entry.getValue());
			}
		}
	}

	private static void jsonArrayInput(JSONObject jsonParent, String name, JSONArray jsonArr) {
		if (null == name || "".equals(name) || jsonArr.isEmpty()) {
			return;
		}
		for (int j = 0; j < jsonArr.size(); j++) {
			String keyName = name + "@@[" + String.format("%06d", j) + "]";
			if (jsonArr.get(j) instanceof JSONArray) {
				jsonArrayInput(jsonParent, keyName, (JSONArray) jsonArr.get(j));
			}
			else if (jsonArr.get(j) instanceof JSONObject) {
				jsonInput(jsonParent, keyName, (JSONObject) jsonArr.get(j));
			}
			else if (null != jsonArr.get(j)/* &&jsonArr.get(j).toString().length()>0 */) {
				jsonParent.put(keyName, jsonArr.get(j));
			}
		}
	}

	public static Map<String, String> getPathParamMap(JSONObject req, Long ts, Map thirdData) {
		if (null == ts || "".equals(ts.toString())) {
			return null;
		}
		String appid = thirdData.get("APPID").toString();
		String fmt = thirdData.get("FMT").toString();
		String version = thirdData.get("VERSION").toString();
		Map<String, String> reqMap = new HashMap<String, String>();
		String sig = getSin(req, ts, thirdData);
		/*
		 * if(null==sig) { return null; }
		 */
		if (null == req || req.isEmpty()) {
			reqMap.put("req", "{}");
		}
		else {
			reqMap.put("req", req.toJSONString());
		}
		reqMap.put("appid", appid);
		reqMap.put("v", version);
		reqMap.put("ts", ts.toString());
		reqMap.put("sig", sig);
		reqMap.put("fmt", fmt);
		return reqMap;
	}

}

package com.tzx.admin.omp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzx.admin.omp.entity.ErpOmpPayRel;
import com.tzx.admin.omp.entity.ErpPayment;
import com.tzx.admin.omp.entity.ErpWelifePayRel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ErpOmpPayRelMapper extends BaseMapper<ErpOmpPayRel> {

	/**
	 * 同步天子星支付方式到对照表，生成omp_id
	 */
	@Update("call tzxerp.sync_pay_to_omp_rel_table()")
	void syncRel();

	List<ErpPayment> selectErpPaymentList();

	/**
	 * 查询支付方式适用的门店列表, 返回门店的ompId列表
	 */
	List<String> selectOrgOmpIdListByPayId(@Param("payId") Long payId);

	/**
	 * 查询待推送到微生活的支付方式
	 */
	List<ErpPayment> selectToSyncWelifePaymentList();

	/**
	 * 保存微生活/天子星支付方式映射关系.
	 */
	void saveWelifePayRef(ErpWelifePayRel payRel);

}

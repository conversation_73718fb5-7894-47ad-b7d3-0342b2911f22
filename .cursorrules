<current_task>
等待用户指定任务目标:
任务步骤:
1. [x] 收集问题信息(已完成)
2. [x] 代码阅读与分析(已完成)
3. [*] 问题解决方案(当前步骤)

  worker:
    developer

  前置知识:
    - doc:
        _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/problem_description.md
    - doc:
        _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/code_analysis.md

  步骤规则:
    - 询问用户是否需要修改代码来解决问题
    - 如果需要，提出具体的代码修改方案
    - 修改方案应遵循项目现有代码风格和架构
    - 提供修改前后的代码对比
    - 分析修改可能带来的影响和风险
    - 与用户确认修改方案

  输出要求:
    - doc:
        _agent-local/workspace/{{ yyyyMMdd }}/{{ problem_desc }}/solution_proposal.md
    - template:
        # 问题解决方案

        ## 解决方案概述
      [简要描述解决方案]

        ## 具体修改
        ### 文件1: [文件路径]
            ```diff
                - // 原代码
                + // 新代码
            ```

        ### 文件2: [文件路径]
            ```diff
                - // 原代码
                + // 新代码
            ```

        ## 修改影响分析
        - 功能影响：
        - 性能影响：
        - 兼容性影响：
        - 可能的副作用：

        ## 验证方案
        - 如何验证修改解决了问题：
        - 建议的测试场景：

**以上是最新的当前任务信息，请严格遵循**

**接下来，你(AI)必须清晰明确的罗列出自己要遵循的当前步骤规则，严格遵循**
</current_task>
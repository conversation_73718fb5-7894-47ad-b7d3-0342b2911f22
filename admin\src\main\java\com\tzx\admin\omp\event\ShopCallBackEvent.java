package com.tzx.admin.omp.event;

import com.tzx.admin.omp.OmpProperties;
import com.tzx.admin.omp.entity.OmpShopEntity;
import com.tzx.admin.utils.OmpUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import static com.tzx.admin.constants.OmpConstant.POS_ORDER;

@Getter
public class ShopCallBackEvent extends ApplicationEvent {

	@Setter
	private String bbrq;

	@Setter
	private Integer jgxh;

	private final String ossPath;

	private final OmpProperties ompProperties;

	private final OmpShopEntity ompShopEntity;

	public ShopCallBackEvent(Object source, Integer jgxh, String bbrq, String ossPath, OmpProperties ompProperties,
			OmpShopEntity ompShopEntity) {
		super(source);
		this.jgxh = jgxh;
		this.ossPath = ossPath;
		this.bbrq = bbrq;
		this.ompProperties = ompProperties;
		this.ompShopEntity = ompShopEntity;
	}

	@Override
	public String toString() {
		return "ShopCallBackEvent{" + "bbrq='" + bbrq + '\'' + ", jgxh=" + jgxh + ", ossPath='" + ossPath + '\''
				+ ", ompProperties=" + ompProperties + ", ompShopEntity=" + ompShopEntity + '}';
	}

	public String exec() {
		return OmpUtils.syncOmpShopNotice(ompProperties, ompShopEntity.getOmpBranchId(), ompShopEntity.getShopOmpId(),
				POS_ORDER, ossPath, bbrq);
	}

}

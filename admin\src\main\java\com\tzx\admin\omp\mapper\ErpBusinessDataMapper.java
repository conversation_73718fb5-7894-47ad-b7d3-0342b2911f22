package com.tzx.admin.omp.mapper;

import com.tzx.admin.omp.entity.ErpDisCountData;
import com.tzx.admin.omp.entity.ErpPaymentData;
import com.tzx.admin.omp.entity.ErpShopData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ErpBusinessDataMapper {

	List<ErpDisCountData> queryErpDisCountData(@Param("bbrq") String bbrq, @Param("jgxh") Integer jgxh);

	List<ErpPaymentData> queryErPaymentData(@Param("bbrq") String bbrq, @Param("jgxh") Integer jgxh);

	List<ErpShopData> queryErpShopData(@Param("bbrq") String bbrq, @Param("dayEndFlag") Integer dayEndFlag);

	List<ErpShopData> queryDayEndNoOssData(@Param("bbrq") String bbrq);

}

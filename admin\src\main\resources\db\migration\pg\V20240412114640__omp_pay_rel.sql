create table if not exists tzxerp.erp_omp_pay_rel(
    omp_id bigint not null  primary key,
    tzx_pay_id character varying(50) not null,
    tzx_pay_type character varying(10) not null,
    sync_status character varying(5) not null default  '0',
    created_at timestamp without time zone not null,
    updated_at timestamp without time zone not null,
    deleted_at timestamp without time zone
);
comment on table tzxerp.erp_omp_pay_rel is 'omp支付关系表';
comment on column tzxerp.erp_omp_pay_rel.omp_id is 'omp_id';
comment on column tzxerp.erp_omp_pay_rel.tzx_pay_id is '支付id';
comment on column tzxerp.erp_omp_pay_rel.tzx_pay_type is '支付类型,FK付款，YH 优惠';
comment on column tzxerp.erp_omp_pay_rel.sync_status is '同步状态,0未同步，1已同步';
comment on column tzxerp.erp_omp_pay_rel.created_at is '创建时间';
comment on column tzxerp.erp_omp_pay_rel.updated_at is '更新时间';
comment on column tzxerp.erp_omp_pay_rel.deleted_at is '删除时间';
-- 唯一索引  tzx_pay_id ,tzx_pay_type
create unique index if not exists omp_pay_rel_tzx_pay_id_uk on tzxerp.erp_omp_pay_rel(tzx_pay_id,tzx_pay_type);
create sequence  if not exists omp_pay_rel_seq start with 10000 increment by 1;

-- 支付方式同步存贮过程
CREATE OR REPLACE PROCEDURE sync_pay_to_omp_rel_table()
    LANGUAGE plpgsql AS
$$
declare payment record;
declare exist_record Boolean;
BEGIN
    -- 打开针对erp_payments表的游标
    FOR payment IN select t.id,t.ylc,t.fkfsbh from tzxerp.erp_payments t where t.id not in (select tzx_pay_id::bigint from tzxerp.erp_omp_pay_rel where tzx_pay_type = 'FK') LOOP
        -- 微信：2-- 支付宝：1-- 网商银行：613-- 美团外卖：992-- 饿了么外卖：996-- 美团团购券：7
        -- 如果 ylc=ERP_FKFS_ZFB,则 id = 1
            if payment.ylc = 'ERP_FKFS_ZFB' then --支付宝
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (1,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.ylc = 'ERP_FKFS_WX' then -- 微信
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (2,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.ylc = 'ERP_FKFS_MT' then -- 美团券
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (7,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.ylc = 'ERP_FKFS_WSYH' then -- 网商银行
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (613,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.fkfsbh = '1986' then -- 美团外卖
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (992,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.fkfsbh = '1985' then -- 饿了么外卖
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (996,cast(payment.id as text) ,'FK','0',now(),now());
            else
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (nextval('omp_pay_rel_seq'),cast(payment.id as text) ,'FK','0',now(),now());
            end if;
        END LOOP;
        -- 映射关系表记录不存在则新加网商银行
        select EXISTS(select tzx_pay_id::bigint from tzxerp.erp_omp_pay_rel where tzx_pay_type = 'FK' and omp_id=613) into exist_record;
        IF  NOT exist_record THEN
        insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (613,'TZX_WSYH_01' ,'FK','0',now(),now());
        END IF;
        -- 映射关系表记录不存在则新加中信银行
        select EXISTS(select tzx_pay_id::bigint from tzxerp.erp_omp_pay_rel where tzx_pay_type = 'FK' and omp_id=614) into exist_record;
        IF  NOT exist_record THEN
        insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (614,'TZX_ZXYH_01' ,'FK','0',now(),now());
        END IF;
    END
$$;

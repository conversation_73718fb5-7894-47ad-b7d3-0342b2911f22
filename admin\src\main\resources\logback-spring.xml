<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false">
	<!-- 从 spring 配置文件读取日志文件位置，默认存放路径为 logs/模块artifactId 下 -->
	<!-- project.artifactId 的值由 maven resource 插件在打包时动态替换 -->
	<springProperty name="log.path" source="logging.file.path" defaultValue="logs/${project.artifactId}"/>
	<!-- 活动文件的大小 -->
	<property name="max.file.size" value="500MB"/>
	<!-- 保留的归档文件的最大数量 -->
	<property name="max.history" value="30"/>
	<!-- 控制所有归档日志文件的总大小 -->
	<property name="total.size.cap" value="30GB"/>

	<!-- name的值是变量的名称，value的值时变量定义的值。通过定义的值会被插入到logger上下文中。定义后，可以使“${}”来使用变量。 -->
	<!-- 彩色日志格式 -->
	<property name="CONSOLE_LOG_PATTERN"
			  value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%X{traceId}]){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
	<!-- 彩色日志依赖的渲染类 -->
	<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
	<conversionRule conversionWord="wex"
					converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
	<conversionRule conversionWord="wEx"
					converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
	<!--1. 输出到控制台-->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!--2. 输出到文档-->
	<!-- 2.1 所有级别的日志，时间滚动输出  -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文档的路径及文档名 -->
		<file>${log.path}/output.log</file>
		<!--日志文档输出格式-->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset> <!-- 设置字符集 -->
		</encoder>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 日志归档 -->
			<fileNamePattern>${log.path}/%d{yyyy-MM, aux}/output.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<!--日志文档最大大小-->
			<maxFileSize>${max.file.size}</maxFileSize>
			<!--日志文档保留天数-->
			<maxHistory>${max.history}</maxHistory>
			<!-- 日志文件最大占用存储 -->
			<totalSizeCap>${total.size.cap}</totalSizeCap>
		</rollingPolicy>
	</appender>

	<!-- 2.2 level为 ERROR 日志，时间滚动输出  -->
	<appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文档的路径及文档名 -->
		<file>${log.path}/error.log</file>
		<!--日志文档输出格式-->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset> <!-- 设置字符集 -->
		</encoder>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 日志归档 -->
			<fileNamePattern>${log.path}/%d{yyyy-MM, aux}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<!--日志文档最大大小-->
			<maxFileSize>${max.file.size}</maxFileSize>
			<!--日志文档保留天数-->
			<maxHistory>${max.history}</maxHistory>
			<!-- 日志文件最大占用存储 -->
			<totalSizeCap>${total.size.cap}</totalSizeCap>
		</rollingPolicy>
		<!-- 此日志文档只记录error级别的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>error</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!--
		root节点是必选节点，用来指定最基础的日志输出级别，只有一个level属性
		level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，
		不能设置为INHERITED或者同义词NULL。默认是DEBUG
		可以包含零个或多个元素，标识这个appender将会添加到这个logger。
	-->
	<!-- 本地和开发环境:打印控制台-->
	<springProfile name="dev | local">
		<root level="INFO">
			<appender-ref ref="CONSOLE"/>
		</root>
	</springProfile>

	<!-- 测试或生产:输出到文档 -->
	<springProfile name="!(dev | local)">
		<root level="INFO">
			<appender-ref ref="FILE"/>
			<appender-ref ref="ERROR_FILE"/>
		</root>
	</springProfile>

	<logger name="org.apache.http.impl.conn" level="INFO"/>
	<logger name="com.zaxxer.hikari.pool" level="INFO"/>
</configuration>

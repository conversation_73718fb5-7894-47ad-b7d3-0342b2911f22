package com.tzx.admin.omp.oss;

import com.hccake.ballcat.common.oss.DefaultOssTemplate;
import com.hccake.ballcat.common.oss.OssProperties;

import java.nio.file.Paths;

public class Test {

	public static void main(String[] args) throws Exception {
		OssProperties ossProperties = new OssProperties();
		//
		ossProperties.setEndpoint("https://profiles-omp-pro.oss-cn-beijing.aliyuncs.com");
		ossProperties.setBucket("profiles-omp-pro");
		ossProperties.setPathStyleAccess(true);
		ossProperties.setAccessSecret("xx");
		ossProperties.setAccessKey("xxx");
		ossProperties.setRegion("cn-beijing");
		ossProperties.setEnabled(true);

		DefaultOssTemplate defaultOssTemplate = new DefaultOssTemplate(ossProperties);
		defaultOssTemplate.afterPropertiesSet();
		String key = "payment/939dd48b-7cd3-45eb-b7ba-f798678724eb/V1.0/paymentinfo.json";

		defaultOssTemplate.putObject("notomp", key, Paths.get("xxx").toFile());
	}

}

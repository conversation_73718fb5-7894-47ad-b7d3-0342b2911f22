package com.tzx.admin.utils;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.google.gson.Gson;
import com.tzx.admin.omp.OmpProperties;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.tzx.admin.constants.OmpConstant.*;

@Slf4j
public final class OmpUtils {

	static final Gson GSON = new Gson();

	private OmpUtils() {
	}

	public static File packageJson2OSSFile(String jsonStr) throws RuntimeException {
		log.debug("packageJson2OSS json:{}", jsonStr);
		String tmpFileName = UUID.randomUUID().toString();
		try {
			Path path = Paths.get(String.format("/tmp/%s.json", tmpFileName));
			if (Files.exists(path)) {
				Files.delete(path);
			}
			Files.write(path, jsonStr.getBytes(StandardCharsets.UTF_8));
			return path.toFile();
		}
		catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static File createOssTempFile(String jsonStr) throws IOException {

		Path tempFile = Files.createTempFile("oss_", ".json");
		Files.write(tempFile, jsonStr.getBytes(StandardCharsets.UTF_8), StandardOpenOption.WRITE);
		return tempFile.toFile();
	}

	public static String syncOmpShopNotice(OmpProperties ompProperties, String branchId, String shopOmpId, String type,
			String objectName, String bbrq) {
		String url = ompProperties.getOpenApiUrl();
		String merchantId = ompProperties.getMerchantId();
		String appId = ompProperties.getAppId();
		String appKey = ompProperties.getAppKey();

		String rest;
		url = url + OSS_NOTIFY;
		String md5Str = String.format(OSS_OMP_APP_SIGN_STR, appId, branchId, bbrq, merchantId, shopOmpId);
		String sign = Md5Util.md5Little(md5Str + appKey);
		log.info("加密前：{}{}", md5Str, appKey);
		log.info("加密后：{}", sign);
		String queryString = String.format(OSS_NOTIFY_PARAM_STR, appId, branchId, bbrq, merchantId, shopOmpId, sign);
		log.info("请求OMP发送参数：url = {}---queryString = {}", url, queryString);
		try {
			rest = HttpUtil.post(url + "?" + queryString, "");
			log.info("请求omp响应{}", rest);
		}
		catch (Exception e) {
			log.error("调用OMP异常 url:{} param:{} e:", url, queryString, e);
			throw new RuntimeException("请求OMP错误");
		}

		return rest;
	}

	public static String syncOmpBranchNotice(OmpProperties ompProperties, String branchId, String bbrq) {
		Map<String, String> map = new HashMap<>();

		String url = ompProperties.getOpenApiUrl();
		String merchantId = ompProperties.getMerchantId();
		String appId = ompProperties.getAppId();
		String appKey = ompProperties.getAppKey();
		map.put("appId", appId);
		map.put("bid", branchId);
		map.put("date", bbrq);
		map.put("mid", merchantId);
		String rest;
		url = url + BRANCH_NOTIFY;
		String md5Str = String.format(BRANCH_OMP_APP_SIGN_STR, appId, branchId, bbrq, merchantId);
		String sign = Md5Util.md5Little(md5Str + appKey);
		log.info("加密前：{}{}", md5Str, appKey);
		log.info("加密后：{}", sign);
		String queryString = String.format(BRANCH_NOTIFY_PARAM_STR, appId, branchId, bbrq, merchantId, sign);
		log.info("请求OMP发送参数：url = {}---json = {}", url, map);
		try (HttpResponse execute = HttpUtil.createPost(url + "?" + queryString)
			.body(GSON.toJson(map))
			.timeout(1000)
			.execute()) {
			rest = execute.body();
			log.info("请求omp响应{}", rest);
		}
		catch (Exception e) {
			log.error("调用OMP异常 url:{} param:{} e:", url, queryString, e);
			throw new RuntimeException("请求OMP错误");
		}

		return rest;
	}

}

spring:
  datasource:
    url: ******************************************************************************************************************************
    username: root
    password: '123456'
  redis:
    host: redis
    password: ''
    port: 6379

# 日志文件地址，配置此属性以便 SBA 在线查看日志
logging:
  file:
    path: logs/@artifactId@
    name: ${logging.file.path}/output.log

ballcat:
  oss:
    bucket: your-bucket-here
    endpoint: http://oss-cn-shanghai.aliyuncs.com
    access-key: your key here
    access-secret: your secret here
package com.tzx.admin.omp.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class MenudishespriceItem {

	/**
	 * 排序
	 */
	private Integer fdiOrder;

	/**
	 * 别名字母首拼
	 */
	private String fdcFirstspell;

	/**
	 * 会员价
	 */
	private Integer fdmMemberprice;

	/**
	 * 菜品id
	 */
	private Integer fdiDishesid;

	/**
	 * 售卖周期：日期不限的话存空字符串
	 */
	private String fdcSellcycle;

	/**
	 * C端显示类id
	 */
	private Integer fdiRecccid;

	/**
	 * 售卖时间段
	 */
	private String fdcSelltime;

	/**
	 * 菜谱id
	 */
	private Integer fdiMenuid;

	/**
	 * 更新时间
	 */
	private String fddUpdatetime;

	/**
	 * 单位id
	 */
	private Integer fdiDnid;

	/**
	 * 餐盒数量，默认一元一个
	 */
	private Integer fdmBoxtotal;

	/**
	 * 标记菜单ids
	 */
	private String fdcFlagmenuids;

	/**
	 * 主键id
	 */
	private Integer id;

	/**
	 * 单价
	 */
	private Integer fdmPrice;

	/**
	 * 成本价
	 */
	private Integer fdmRatedcost;

	@JsonIgnore
	private String brandid;

}
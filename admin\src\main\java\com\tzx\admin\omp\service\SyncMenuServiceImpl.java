package com.tzx.admin.omp.service;

import com.google.gson.Gson;
import com.hccake.ballcat.common.oss.OssTemplate;
import com.tzx.admin.omp.OmpProperties;
import com.tzx.admin.omp.SyncMenuService;
import com.tzx.admin.omp.entity.*;
import com.tzx.admin.omp.mapper.OmpOssMenuMapper;
import com.tzx.admin.utils.OmpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.tzx.admin.constants.OmpConstant.*;

/**
 * SyncMenuServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-11-15
 */

@Service
@Slf4j
public class SyncMenuServiceImpl implements SyncMenuService {

	private static final Gson GSON = new Gson();

	private static final String DICTIONARY_KEY = "dictionary";

	private static final String SPEC_KEY = "spec";

	private static final String DISH_CATEGORY_KEY = "reportcategory";

	private static final String COMBO_CATEGORY_KEY = "packcategory";

	private static final String PRACTICE_KEY = "practice";

	private static final String DISHES_KEY = "dishes";

	private static final String MENU_KEY = "menu";

	private static final String SETMEAL_SINGLE_MAP = "setmealSingleMap";

	private static final String SETMEAL_GROUP_MAP = "setmealGroupMap";

	private static final String GROUP_SINGLE_MAP = "groupSingleMap";

	private final String MXLX_SINGLE = "ERP_MXLX_SINGLE";

	private final String MXLX_GROUP = "ERP_MXLX_GROUP";

	private final String MXLX_GROUP_SINGLE = "ERP_MXLX_GROUP_SINGLE";

	private final OmpOssMenuMapper ompOssMenuMapper;

	private final Map<Integer, OmpShopEntity> tzxOmpShopMapping;

	private final OmpProperties ompProperties;

	private final OssTemplate profileOmpOssTemplate;

	private final OssTemplate menuOmpOssTemplate;

	private final Map<String, Integer> ompBidMap;

	private final OmpBohIdRefService ompBohIdRefService;

	private final Map<String, String> ompOssBucket;

	public SyncMenuServiceImpl(OmpOssMenuMapper ompOssMenuMapper, Map<Integer, OmpShopEntity> tzxOmpShopMapping,
			OmpProperties ompProperties, OssTemplate profileOmpOssTemplate,
			@Qualifier("menuOmpOssTemplate") OssTemplate menuOmpOssTemplate, Map<String, Integer> ompBidMap,
			OmpBohIdRefService ompBohIdRefService, Map<String, String> ompOssBucket) {
		this.ompOssMenuMapper = ompOssMenuMapper;
		this.tzxOmpShopMapping = tzxOmpShopMapping;
		this.ompProperties = ompProperties;
		this.profileOmpOssTemplate = profileOmpOssTemplate;
		this.menuOmpOssTemplate = menuOmpOssTemplate;
		this.ompBidMap = ompBidMap;
		this.ompBohIdRefService = ompBohIdRefService;
		this.ompOssBucket = ompOssBucket;
	}

	@Override
	public void sync() {
		// 菜品单位
		syncUnit();
		// 营业区域
		List<String> blids = syncBusinessLoc();
		// 菜谱
		syncMenu(blids);
		// 菜品类别
		syncCategory();
		// 做法
		syncPractice();
		// 菜品
		syncDish();
	}

	private void syncDish() {

		List<OmpOssMenuDish> ompOssMenuDish = ompOssMenuMapper.selectDish();

		Map<Integer, List<ErpItemSpec>> itemSpecIdMap = ompOssMenuMapper.selectSpec()
			.stream()
			.collect(Collectors.groupingBy(ErpItemSpec::getItemId));

		final Map<String, Map<Integer, List<ErpPackageDetail>>> erpPackageDetailMap = selectErpPackageDetailMap();

		Map<String, Map<Integer, List<MenudishespriceItem>>> menudishespriceItemMap = selectMenudishespriceItemMap();

		AtomicInteger specId = new AtomicInteger(1);
		AtomicInteger packageDetailId = new AtomicInteger(1);
		AtomicInteger menudishespriceId = new AtomicInteger(1);
		AtomicInteger rpdid = new AtomicInteger(1);

		ompBidMap.keySet().forEach(k -> {

			List<OmpOssMenuDish> ossMenuDishes = new ArrayList<>();

			ompOssMenuDish.forEach(d -> {

				OmpOssMenuDish item = new OmpOssMenuDish();
				BeanUtils.copyProperties(d, item);

				// 处理id

				Integer ompDishId = ompIdConverter(DISHES_KEY, ompBidMap.get(k), item.getDishesid());

				if (0 == item.getPackflag()) { // 单品
					item.setRcid(
							ompIdConverter(DISH_CATEGORY_KEY, ompBidMap.get(k), item.getRcid(), COMBO_CATEGORY_KEY));
				}
				if (1 == item.getPackflag()) { // 套餐
					item.setRcid(
							ompIdConverter(COMBO_CATEGORY_KEY, ompBidMap.get(k), item.getRcid(), DISH_CATEGORY_KEY));
					// 主菜
					item.getPackagedetailed().clear();
					erpPackageDetailMap.get(SETMEAL_SINGLE_MAP)
						.getOrDefault(item.getDishesid(), Collections.emptyList())
						.forEach(i -> {
							OmpOssMenuDish.PackageDetailed detailed = new OmpOssMenuDish.PackageDetailed();
							detailed.setFdbIsdefaultdishes(i.getIsDefault());
							detailed.setFdiDishesid(ompIdConverter(DISHES_KEY, ompBidMap.get(k), i.getCid()));
							detailed
								.setFdiDnid(ompIdConverter(DICTIONARY_KEY, ompBidMap.get(k), i.getDwbh(), SPEC_KEY));
							detailed.setFdiPackaged(packageDetailId.getAndIncrement());
							detailed.setFdiPdid(ompDishId);
							detailed.setFdiPdtype(i.getMxtype());
							detailed.setFdiCount(i.getCmsl());

							item.getPackagedetailed().add(detailed);

						});

					// 辅菜分组
					item.getAuxiliarydishestype().clear();
					erpPackageDetailMap.get(SETMEAL_GROUP_MAP)
						.getOrDefault(item.getDishesid(), Collections.emptyList())
						.forEach(i -> {
							OmpOssMenuDish.AuxiliaryDishesType auxiliaryDishesType = new OmpOssMenuDish.AuxiliaryDishesType();
							auxiliaryDishesType.setCount(i.getCmsl());
							auxiliaryDishesType.setDishesid(ompDishId);
							auxiliaryDishesType.setIsprompty(0);
							auxiliaryDishesType.setMaxcount(i.getMaxCount().intValue());
							auxiliaryDishesType.setMincount(1);
							auxiliaryDishesType.setRpdid(rpdid.getAndIncrement());
							auxiliaryDishesType.setRpdname(i.getCmmc());
							auxiliaryDishesType.setRpdtype(i.getMxtype());

							item.getAuxiliarydishestype().add(auxiliaryDishesType);

							Double maxCount = i.getMaxCount();

							erpPackageDetailMap.get(GROUP_SINGLE_MAP)
								.getOrDefault(i.getCid(), Collections.emptyList())
								.forEach(s -> {

									OmpOssMenuDish.PackageDetailed detailed = new OmpOssMenuDish.PackageDetailed();
									detailed.setFdbIsdefaultdishes(s.getIsDefault());
									detailed.setFdiDishesid(ompIdConverter(DISHES_KEY, ompBidMap.get(k), s.getCid()));
									detailed.setFdiDnid(
											ompIdConverter(DICTIONARY_KEY, ompBidMap.get(k), s.getDwbh(), SPEC_KEY));
									detailed.setFdiPackaged(packageDetailId.getAndIncrement());
									detailed.setFdiPdid(ompDishId);
									detailed.setFdiPdtype(s.getMxtype());
									detailed.setFdiCount(s.getCmsl());
									detailed.setFdiMaxcount(maxCount);
									detailed.setFdiRpdid(auxiliaryDishesType.getRpdid());
									detailed.setFdiRpjiajiaamount(0d);

									item.getPackagedetailed().add(detailed);
								});

						});

				}

				// 价格体系
				final List<MenudishespriceItem> menudishespriceItems = menudishespriceItemMap
					.getOrDefault(k, Collections.emptyMap())
					.getOrDefault(item.getDishesid(), Collections.emptyList());

				menudishespriceItems.forEach(p -> {
					p.setId(menudishespriceId.getAndIncrement());
					p.setFdiDishesid(ompDishId);
					p.setFdiMenuid(ompIdConverter(MENU_KEY, ompBidMap.get(k), p.getFdiMenuid()));
					p.setFdcFlagmenuids(String.valueOf(p.getFdiMenuid()));
					p.setFdmBoxtotal(0);
					p.setFdmRatedcost(0);
					p.setFdiDnid(ompIdConverter(DICTIONARY_KEY, ompBidMap.get(k), item.getDwid(), SPEC_KEY));

				});
				item.setMenudishesprice(menudishespriceItems);

				List<OmpOssMenuDish.MenudishescostItem> menudishescost = new ArrayList<>();

				if (itemSpecIdMap.containsKey(item.getDishesid())) {
					itemSpecIdMap.get(item.getDishesid()).forEach(itemSpec -> {

						OmpOssMenuDish.MenudishescostItem it = new OmpOssMenuDish.MenudishescostItem();
						it.setDishesid(ompDishId);
						it.setDnid(ompIdConverter(SPEC_KEY, ompBidMap.get(k), itemSpec.getSpecId(), DICTIONARY_KEY));
						it.setRatedcost(0);
						it.setId(specId.getAndIncrement());
						menudishescost.add(it);
					});
				}
				else {
					OmpOssMenuDish.MenudishescostItem it = new OmpOssMenuDish.MenudishescostItem();
					it.setDishesid(ompDishId);
					it.setDnid(ompIdConverter(DICTIONARY_KEY, ompBidMap.get(k), item.getDwid(), SPEC_KEY));
					it.setRatedcost(0);
					it.setId(specId.getAndIncrement());
					menudishescost.add(it);

				}

				item.setMenudishescost(menudishescost);

				item.setDishesid(ompDishId);
				item.setBrandid(k);

				ossMenuDishes.add(item);

			});

			Map<String, List<OmpOssMenuDish>> ossData = new HashMap<>();
			ossData.put(DISHES_KEY, ossMenuDishes);

			try {
				File file = OmpUtils.createOssTempFile(GSON.toJson(ossData));
				String ossKey = String.format(OMP_OSS_MENU_MID_BID_DISHES_KEY, ompProperties.getMerchantId(), k,
						"dishes");
				menuOmpOssTemplate.putObject(ompOssBucket.get("menuBucketName"), ossKey, file);
				log.info("dishes上传成功: " + menuOmpOssTemplate.getURL(ompOssBucket.get("menuBucketName"), ossKey));
			}
			catch (IOException e) {
				log.error("上传oss异常", e);
			}
		});

	}

	private Map<String, Map<Integer, List<MenudishespriceItem>>> selectMenudishespriceItemMap() {
		return ompOssMenuMapper.selectMenudishespriceItem()
			.stream()
			.collect(Collectors.groupingBy(MenudishespriceItem::getBrandid,
					Collectors.groupingBy(MenudishespriceItem::getFdiDishesid, Collectors.toList())));
	}

	private void syncMenu(List<String> blids) {
		Map<String, List<OmpOssMenu>> ompOssMenuMenus = ompOssMenuMapper.selectMenu()
			.stream()
			.collect(Collectors.groupingBy(OmpOssMenu::getBrandid));

		Map<String, List<OmpOssMenu>> ossData = new HashMap<>();

		ompOssMenuMenus.forEach((k, v) -> {
			List<OmpOssMenu> list = new ArrayList<>();

			v.forEach(item -> {
				OmpOssMenu menu = new OmpOssMenu();
				BeanUtils.copyProperties(item, menu);
				menu.setMenuid(ompIdConverter(MENU_KEY, ompBidMap.get(k), item.getMenuid()));
				// blids转换为,分隔的字符串
				menu.setBlids(String.join(",", blids));
				list.add(menu);

			});

			ossData.put(MENU_KEY, list);

			try {
				File file = OmpUtils.createOssTempFile(GSON.toJson(ossData));
				String ossKey = String.format(OMP_OSS_MENU_MID_BID_DISHES_KEY, ompProperties.getMerchantId(), k,
						MENU_KEY);
				menuOmpOssTemplate.putObject(ompOssBucket.get("menuBucketName"), ossKey, file);
				log.info("menu上传成功: " + menuOmpOssTemplate.getURL(ompOssBucket.get("menuBucketName"), ossKey));
			}
			catch (IOException e) {
				log.error("上传oss异常", e);
			}
		});

	}

	private void syncPractice() {
		List<OmpOssMenuPractice> practices = ompOssMenuMapper.selectPractice();

		tzxOmpShopMapping.values().stream().map(OmpShopEntity::getOmpBranchId).distinct().forEach(bid -> {
			List<OmpOssMenuPractice> ossMenuPractices = new ArrayList<>();
			practices.forEach(practice -> {
				OmpOssMenuPractice ossMenuPractice = new OmpOssMenuPractice();
				BeanUtils.copyProperties(practice, ossMenuPractice);
				ossMenuPractice.setBrandid(bid);
				ossMenuPractice
					.setPracticeid(ompIdConverter(PRACTICE_KEY, ompBidMap.get(bid), practice.getPracticeid()));
				ossMenuPractices.add(ossMenuPractice);
			});
			Map<String, List<OmpOssMenuPractice>> ossData = new HashMap<>();
			ossData.put(PRACTICE_KEY, ossMenuPractices);

			try {
				File file = OmpUtils.createOssTempFile(GSON.toJson(ossData));
				String ossKey = String.format(OMP_OSS_MENU_MID_BID_DISHES_KEY, ompProperties.getMerchantId(), bid,
						"practice");
				menuOmpOssTemplate.putObject(ompOssBucket.get("menuBucketName"), ossKey, file);
				log.info("practice上传成功: " + menuOmpOssTemplate.getURL(ompOssBucket.get("menuBucketName"), ossKey));
			}
			catch (IOException e) {
				log.error("上传oss异常", e);
			}
		});

	}

	private void syncCategory() {

		final List<OmpOssMenuCategory> ompOssMenuCategoryOne = ompOssMenuMapper.selectCategoryOne();

		Map<String, List<OmpOssMenuCategory>> ossPackCategory = ompOssMenuMapper.selectCategory()
			.stream()
			.collect(Collectors.groupingBy(OmpOssMenuCategory::getBrandid));

		ossPackCategory.forEach((k, v) -> {

			// 菜品类别
			List<OmpOssMenuCategory> list = new ArrayList<>();
			Map<String, List<OmpOssMenuCategory>> ossReportCategoryData = new HashMap<>();
			v.forEach(item -> {
				OmpOssMenuCategory category = new OmpOssMenuCategory();
				BeanUtils.copyProperties(item, category);
				category.setPackflag(0);
				category
					.setRcid(ompIdConverter(DISH_CATEGORY_KEY, ompBidMap.get(k), item.getRcid(), COMBO_CATEGORY_KEY));
				list.add(category);
			});

			ompOssMenuCategoryOne.forEach(item -> {
				OmpOssMenuCategory category = new OmpOssMenuCategory();
				BeanUtils.copyProperties(item, category);
				category.setBrandid(k);
				category.setPackflag(0);
				category
					.setRcid(ompIdConverter(DISH_CATEGORY_KEY, ompBidMap.get(k), item.getRcid(), COMBO_CATEGORY_KEY));
				list.add(category);

			});

			ossReportCategoryData.put(DISH_CATEGORY_KEY, list);

			try {
				File file = OmpUtils.createOssTempFile(GSON.toJson(ossReportCategoryData));
				String ossKey = String.format(OMP_OSS_MENU_MID_BID_DISHES_KEY, ompProperties.getMerchantId(), k,
						DISH_CATEGORY_KEY);
				menuOmpOssTemplate.putObject(ompOssBucket.get("menuBucketName"), ossKey, file);
				log.info(
						"reportcategory上传成功: " + menuOmpOssTemplate.getURL(ompOssBucket.get("menuBucketName"), ossKey));
			}
			catch (IOException e) {
				log.error("上传oss异常", e);
			}

			// 套餐类别
			List<OmpOssMenuCategory> listPackage = new ArrayList<>();
			Map<String, List<OmpOssMenuCategory>> ossDataPackage = new HashMap<>();

			v.forEach(item -> {
				OmpOssMenuCategory category = new OmpOssMenuCategory();
				BeanUtils.copyProperties(item, category);
				category.setPackflag(1);
				category
					.setRcid(ompIdConverter(COMBO_CATEGORY_KEY, ompBidMap.get(k), item.getRcid(), DISH_CATEGORY_KEY));
				listPackage.add(category);
			});

			ompOssMenuCategoryOne.forEach(item -> {
				OmpOssMenuCategory category = new OmpOssMenuCategory();
				BeanUtils.copyProperties(item, category);
				category.setBrandid(k);
				category.setPackflag(1);
				category
					.setRcid(ompIdConverter(COMBO_CATEGORY_KEY, ompBidMap.get(k), item.getRcid(), DISH_CATEGORY_KEY));
				listPackage.add(category);

			});

			ossDataPackage.put(COMBO_CATEGORY_KEY, listPackage);

			try {
				File file = OmpUtils.createOssTempFile(GSON.toJson(ossDataPackage));
				String ossKey = String.format(OMP_OSS_MENU_MID_BID_DISHES_KEY, ompProperties.getMerchantId(), k,
						COMBO_CATEGORY_KEY);
				menuOmpOssTemplate.putObject(ompOssBucket.get("menuBucketName"), ossKey, file);
				log.info("packcategory上传成功: " + menuOmpOssTemplate.getURL(ompOssBucket.get("menuBucketName"), ossKey));
			}
			catch (IOException e) {
				log.error("上传oss异常", e);
			}

		});

	}

	private void syncUnit() {
		List<OmpOssMenuUnit> ossMenuUnits = new ArrayList<>();
		List<OmpOssMenuUnit> units = ompOssMenuMapper.selectUnit();

		ompBidMap.keySet().forEach(bid -> {
			units.forEach(unit -> {
				OmpOssMenuUnit ossMenuUnit = new OmpOssMenuUnit();
				BeanUtils.copyProperties(unit, ossMenuUnit);
				ossMenuUnit.setBrandId(bid);
				ossMenuUnit.setBrandids(bid);

				if (String.valueOf(ossMenuUnit.getDnid()).equals(ossMenuUnit.getDnno())) {
					ossMenuUnit.setDnid(ompIdConverter(DICTIONARY_KEY, ompBidMap.get(bid), unit.getDnid(), SPEC_KEY));
				}
				else {
					ossMenuUnit.setDnid(ompIdConverter(SPEC_KEY, ompBidMap.get(bid), unit.getDnid(), DICTIONARY_KEY));
				}
				ossMenuUnits.add(ossMenuUnit);

			});
		});

		Map<String, List<OmpOssMenuUnit>> ossData = new HashMap<>();
		ossData.put(DICTIONARY_KEY, ossMenuUnits);

		try {
			File file = OmpUtils.createOssTempFile(GSON.toJson(ossData));
			String ossKey = String.format(OMP_OSS_PROFILES_DICTIONARY_KEY, ompProperties.getMerchantId());
			profileOmpOssTemplate.putObject(OMP_OSS_PROFILES_BUCKET_NOTOMP, ossKey, file);
			log.info("dictionary上传成功: " + profileOmpOssTemplate.getURL(OMP_OSS_PROFILES_BUCKET_NOTOMP, ossKey));
		}
		catch (IOException e) {
			log.error("上传oss异常", e);
		}
	}

	/**
	 * bohid转换为ompid
	 * @param name 类别
	 * @param bid 品牌id
	 * @param id bohid
	 * @return ompid
	 */
	public Integer ompIdConverter(String name, Integer bid, Integer id) {
		return ompIdConverter(name, bid, id, null);
	}

	public Integer ompIdConverter(String name, Integer bid, Integer id, String... unionName) {
		if (null != unionName && unionName.length > 0) {
			// 将name添加到unionName
			unionName = Arrays.copyOf(unionName, unionName.length + 1);
			unionName[unionName.length - 1] = name;
		}
		else {
			unionName = new String[] { name };
		}
		return ompBohIdRefService.getOrCreateOmpId(name, bid, id, unionName);
	}

	private Map<String, Map<Integer, List<ErpPackageDetail>>> selectErpPackageDetailMap() {

		// 套餐-单品关系map
		Map<Integer, List<ErpPackageDetail>> setmealSingleMap = new HashMap<>();
		// 套餐-项目组关系map
		Map<Integer, List<ErpPackageDetail>> setmealGroupMap = new HashMap<>();
		// 项目组-单品关系map
		Map<Integer, List<ErpPackageDetail>> groupSingleMap = new HashMap<>();

		ompOssMenuMapper.selectPackageDetail().forEach(item -> {

			if (MXLX_SINGLE.equals(item.getMxlx())) {
				setmealSingleMap.compute(item.getPid(), (k, v) -> {
					if (v == null) {
						v = new ArrayList<>();
					}
					v.add(item);
					return v;
				});

			}

			if (MXLX_GROUP.equals(item.getMxlx())) {
				setmealGroupMap.compute(item.getPid(), (k, v) -> {
					if (v == null) {
						v = new ArrayList<>();
					}
					v.add(item);
					return v;
				});

			}

			if (MXLX_GROUP_SINGLE.equals(item.getMxlx())) {
				groupSingleMap.compute(item.getPid(), (k, v) -> {
					if (v == null) {
						v = new ArrayList<>();
					}
					v.add(item);
					return v;
				});

			}
		});

		Map<String, Map<Integer, List<ErpPackageDetail>>> ErpPackageDetailMap = new HashMap<>();
		ErpPackageDetailMap.put(SETMEAL_SINGLE_MAP, setmealSingleMap);
		ErpPackageDetailMap.put(SETMEAL_GROUP_MAP, setmealGroupMap);
		ErpPackageDetailMap.put(GROUP_SINGLE_MAP, groupSingleMap);

		return ErpPackageDetailMap;
	}

	private List<String> syncBusinessLoc() {
		List<String> blids = new ArrayList<>();
		AtomicInteger blid = new AtomicInteger(1);
		tzxOmpShopMapping.values().stream().filter(s -> StringUtils.isNotEmpty(s.getShopOmpId())).forEach(s -> {
			OmpOssBusinessLoc businessLoc = new OmpOssBusinessLoc();
			businessLoc.setBlid(blid.getAndIncrement());
			businessLoc.setBlname("默认区域");
			businessLoc.setBlno("01");
			businessLoc.setOgnid(s.getShopOmpId());
			businessLoc.setOrder(1);
			businessLoc.setStatus(0);

			blids.add(String.valueOf(businessLoc.getBlid()));

			Map<String, List<OmpOssBusinessLoc>> ossData = new HashMap<>();
			ossData.put("businessloc", Collections.singletonList(businessLoc));

			try {
				File file = OmpUtils.createOssTempFile(GSON.toJson(ossData));
				String ossKey = String.format(OMP_OSS_PROFILES_BUSINESSLOC_KEY, ompProperties.getMerchantId(),
						s.getOmpBranchId(), s.getShopOmpId(), "businessloc");
				profileOmpOssTemplate.putObject(OMP_OSS_PROFILES_BUCKET_NOTOMP, ossKey, file);
				log.info("businessloc上传成功: " + profileOmpOssTemplate.getURL(OMP_OSS_PROFILES_BUCKET_NOTOMP, ossKey));
			}
			catch (IOException e) {
				log.error("上传oss异常", e);
			}

		});
		return blids;
	}

}

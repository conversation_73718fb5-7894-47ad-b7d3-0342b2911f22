package com.tzx.admin.omp.event;

import com.tzx.admin.omp.entity.OmpBranchPushEntity;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * 校验当前日期是否全部的都已推送omp如果推送 则回调omp 没有推送的话不做处理
 */
@Getter
public class CheckAllPushEvent extends ApplicationEvent {

	private final Set<OmpBranchPushEntity> branchIds;

	public CheckAllPushEvent(Object source, Set<OmpBranchPushEntity> branchIds) {
		super(source);
		this.branchIds = branchIds;
	}

}

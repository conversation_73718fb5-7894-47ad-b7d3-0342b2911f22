package com.tzx.admin.utils;

import com.tzx.common.exception.SystemErrorCode;
import com.tzx.common.exception.SystemException;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.httpclient.*;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.httpclient.util.IdleConnectionTimeoutThread;
import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.ConnectException;
import java.net.URLDecoder;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

public class HttpClientUtil {

	private static Logger LOG = LoggerFactory.getLogger(HttpClientUtil.class);

	private static int defaultConnectionTimeout = 8000;

	private static final long defaultHttpConnectionManagerTimeout = 3 * 1000;

	/**
	 * 回应超时时间, 由bean factory设置，缺省为10秒钟
	 */
	private static int defaultSoTimeout = 10000;

	/**
	 * 闲置连接超时时间, 由bean factory设置，缺省为60秒钟
	 */
	private static int defaultIdleConnTimeout = 60000;

	private static int defaultMaxConnPerHost = 30;

	private static int defaultMaxTotalConn = 80;

	private static HttpConnectionManager connectionManager;

	static {
		try {
			if (connectionManager == null)// 创建一个线程安全的HTTP连接池
			{
				connectionManager = new MultiThreadedHttpConnectionManager();
				connectionManager.getParams().setDefaultMaxConnectionsPerHost(defaultMaxConnPerHost);
				connectionManager.getParams().setMaxTotalConnections(defaultMaxTotalConn);
				IdleConnectionTimeoutThread ict = new IdleConnectionTimeoutThread();
				ict.addConnectionManager(connectionManager);
				ict.setConnectionTimeout(defaultIdleConnTimeout);
				ict.start();
			}
		}
		catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 发送HTTP请求
	 * @param url
	 * @param propsMap 发送的参数
	 */
	public static String httpPost(String url, Map<String, String> propsMap) {
		LOG.info("请求地址" + url + ":" + propsMap.toString());
		String responseMsg = "";
		JSONObject result = new JSONObject();
		HttpClient httpClient = new HttpClient();
		PostMethod postMethod = new PostMethod(url);// POST请求
		if (propsMap != null) {
			// 参数设置
			Set<String> keySet = propsMap.keySet();
			NameValuePair[] postData = new NameValuePair[keySet.size()];
			int index = 0;
			for (String key : keySet) {
				postData[index++] = new NameValuePair(key, propsMap.get(key).toString());
			}
			postMethod.addParameters(postData);
		}
		postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "utf-8");
		postMethod.addRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
		try {
			int statusCode = httpClient.executeMethod(postMethod);// 发送请求
			result.put("code", statusCode);
			if (statusCode == HttpStatus.SC_OK) {
				// 读取内容
				byte[] responseBody = postMethod.getResponseBody();
				// 处理返回的内容
				responseMsg = new String(responseBody, "utf-8");
			}
		}
		catch (HttpException e) {
			e.printStackTrace();
		}
		catch (IOException e) {
			e.printStackTrace();
		}
		finally {
			postMethod.releaseConnection();// 关闭连接
		}
		result.put("msg", responseMsg);
		LOG.info("请求地址" + url + "返回信息:" + responseMsg);
		return result.toString();
	}

	/**
	 * 1 HttpClientByPost
	 * @param checkURL
	 * @param paramsJson
	 * @return
	 * @throws Exception
	 */
	public static String HttpClientByPost(String checkURL, String paramsJson) throws Exception {
		String responseContent = "";
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		PostMethod postMethod = new PostMethod(checkURL);

		try {
			LOG.info("请求地址" + checkURL + ":" + paramsJson);

			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);

			postMethod.setRequestEntity(new StringRequestEntity(paramsJson, "application/json", "UTF-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK) {
				throw SystemException.getInstance(SystemErrorCode.MQ_COMMUNICATION_ERROR)
					.set("STATUS_LINE", postMethod.getStatusLine().toString());
			}

			// responseContent = postMethod.getResponseBodyAsString();
			InputStream resStream = postMethod.getResponseBodyAsStream();
			responseContent = new String(inputStream2String(resStream));

			/*
			 * BufferedReader br = new BufferedReader(new InputStreamReader(resStream));
			 * StringBuffer resBuffer = new StringBuffer(); String resTemp = ""; while
			 * ((resTemp = br.readLine()) != null) { resBuffer.append(resTemp); }
			 * responseContent = resBuffer.toString();
			 */
			LOG.info("请求地址" + checkURL + "返回信息:" + responseContent);

		}
		catch (ConnectException e) {
			LOG.error("无法连接，调用地址:" + checkURL);
		}
		catch (ConnectTimeoutException e) {
			LOG.error("连接超时，调用地址:" + checkURL);
		}
		finally {
			postMethod.releaseConnection();
		}
		return responseContent;
	}

	protected static String inputStream2String(InputStream is) throws UnsupportedEncodingException {
		BufferedReader in = new BufferedReader(new InputStreamReader(is, "UTF-8"));
		StringBuffer buffer = new StringBuffer();
		String line = "";
		try {
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
		}
		catch (IOException e) {
			e.printStackTrace();
		}
		return buffer.toString();
	}

	/**
	 * @param @param request
	 * @param @return
	 * @param @throws IOException
	 * @param @throws UnsupportedEncodingException
	 * @return Map<String, String>
	 * @throws
	 * @Description: Json转换成Map
	 * <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-11-6
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String> getParameterJsonToMap(HttpServletRequest request)
			throws IOException, UnsupportedEncodingException {
		BufferedReader br = new BufferedReader(new InputStreamReader(request.getInputStream()));
		String line = null;
		StringBuilder sb = new StringBuilder();
		while ((line = br.readLine()) != null) {
			sb.append(line);
		}
		String reqBody = sb.toString();
		Map<String, String> json = (Map<String, String>) JSONObject.fromObject(URLDecoder.decode(reqBody, "UTF-8"));
		return json;
	}

	/**
	 * @param @param request
	 * @param @return
	 * @return Map<String, String>
	 * @throws
	 * @Description: 接收Map参数
	 * <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-11-6
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static Map<String, String> getParameterMap(HttpServletRequest request) {
		Map properties = request.getParameterMap();
		Map returnMap = new HashMap();
		Iterator entries = properties.entrySet().iterator();
		Map.Entry entry;
		String name = "";
		String value = "";
		while (entries.hasNext()) {
			entry = (Map.Entry) entries.next();
			name = (String) entry.getKey();
			Object valueObj = entry.getValue();
			if (null == valueObj) {
				value = "";
			}
			else if (valueObj instanceof String[]) {
				String[] values = (String[]) valueObj;
				for (int i = 0; i < values.length; i++) {
					value = values[i] + ",";
				}
				value = value.substring(0, value.length() - 1);
			}
			else {
				value = valueObj.toString();
			}
			returnMap.put(name, value);
		}
		return returnMap;
	}

	/**
	 * 1 HttpClientByPost
	 * @param checkURL
	 * @param paramsJson
	 * @return
	 * @throws Exception
	 */
	public static String HttpRequestByPost(String checkURL, String paramsJson) throws Exception {
		String responseContent = "";
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		PostMethod postMethod = new PostMethod(checkURL);

		try {
			LOG.info("请求地址" + checkURL + ":" + paramsJson);

			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);

			postMethod.setRequestEntity(new StringRequestEntity(paramsJson, "application/json", "UTF-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK) {
				throw SystemException
					.getInstance("接口响应失败 statusCode:" + statusCode, SystemErrorCode.THIRD_INTERFACE_ERROR)
					.set("STATUS_LINE", postMethod.getStatusLine().toString());
			}
			InputStream resStream = postMethod.getResponseBodyAsStream();
			responseContent = new String(inputStream2String(resStream));
			LOG.info("请求地址" + checkURL + "返回信息:" + responseContent);

		}
		catch (ConnectException e) {
			LOG.error("无法连接，调用地址:" + checkURL);
			throw e;
		}
		catch (ConnectTimeoutException e) {
			LOG.error("连接超时，调用地址:" + checkURL);
			throw e;
		}
		finally {
			postMethod.releaseConnection();
		}
		return responseContent;
	}

	public static final int CONNECTION_TIMEOUT = 10000;// 连接超时

	public static final int READDATA_TIMEOUT = 20000;// 数据读取等待超时

	public static final String UTF8 = "UTF-8";// 编码格式

	public static final String PLAIN_TEXT_TYPE = "text/plain";

	private static Log log = LogFactory.getLog(HttpClientUtil.class);

	/**
	 * 无需本地证书keyStore的SSL https带参数请求
	 */
	public static String postSSLUrlWithParams(String url, Map<String, String> reqMap) {
		CloseableHttpClient httpClient = HttpClientUtil.createSSLInsecureClient();
		HttpPost post = new HttpPost(url);
		// 添加参数
		MultipartEntityBuilder builder = MultipartEntityBuilder.create();
		// 决中文乱码
		ContentType contentType = ContentType.create(PLAIN_TEXT_TYPE, UTF8);
		Charset chars = Charset.forName("utf8"); // Setting up the encoding
		builder.setCharset(chars);
		builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);

		if (reqMap != null && reqMap.keySet().size() > 0) {
			Iterator<Map.Entry<String, String>> iter = reqMap.entrySet().iterator();
			while (iter.hasNext()) {
				Map.Entry<String, String> entity = iter.next();
				builder.addTextBody(entity.getKey(), entity.getValue().toString(), contentType);
			}
		}
		StringBuilder sb = new StringBuilder();
		BufferedReader br = null;
		try {
			// 设置客户端请求的头参数getParams已经过时,现在用requestConfig对象替换
			// httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT,CONNECTION_TIMEOUT);
			RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(CONNECTION_TIMEOUT)
				.setSocketTimeout(READDATA_TIMEOUT)
				.build();
			post.setConfig(requestConfig);
			HttpEntity entity = builder.build();
			post.setEntity(entity);

			HttpResponse response = httpClient.execute(post);
			HttpEntity httpEntity = response.getEntity();
			br = new BufferedReader(new InputStreamReader(httpEntity.getContent(), UTF8));
			String s = null;
			while ((s = br.readLine()) != null) {
				sb.append(s);
			}
		}
		catch (UnsupportedEncodingException e) {
			log.error("编码格式输入错误", e);
			throw new RuntimeException("指定的编码集不对,您目前指定的编码集是:" + UTF8);
		}
		catch (ClientProtocolException e) {
			e.printStackTrace();
		}
		catch (IOException e) {
			log.error("读取流文件异常", e);
			throw new RuntimeException("读取流文件异常", e);
		}
		catch (Exception e) {
			log.error("通讯未知系统异常", e);
			throw new RuntimeException("通讯未知系统异常", e);
		}
		finally {
			if (br != null) {
				try {
					br.close();
				}
				catch (IOException e) {
					log.error("关闭br异常" + e);
					e.printStackTrace();
				}
			}
		}
		return sb.toString();
	}

	/**
	 * 创建一个SSL信任所有证书的httpClient对象
	 */
	public static CloseableHttpClient createSSLInsecureClient() {
		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				// 默认信任所有证书
				public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
					return true;
				}
			}).build();
			// AllowAllHostnameVerifier: 这种方式不对主机名进行验证，验证功能被关闭，是个空操作(域名验证)
			SSLConnectionSocketFactory sslcsf = new SSLConnectionSocketFactory(sslContext,
					SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
			return HttpClients.custom().setSSLSocketFactory(sslcsf).build();
		}
		catch (KeyManagementException e) {
			e.printStackTrace();
		}
		catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		catch (KeyStoreException e) {
			e.printStackTrace();
		}
		return HttpClients.createDefault();
	}

	public static String httpGet(String url, String encoding) throws Exception {
		HttpMethod httpMethod = null;
		try {
			HttpClient httpClient = new HttpClient();
			httpClient.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, encoding);
			httpMethod = new GetMethod(url);
			httpMethod.getParams().setCredentialCharset(encoding);// get模式且不带上传文件
			long beginTime = System.currentTimeMillis();
			int httpStat = httpClient.executeMethod(httpMethod);
			System.out.println("返回状态值======" + httpStat);
			if (httpStat == HttpStatus.SC_OK) {
				return IOUtils.toString(httpMethod.getResponseBodyAsStream(), encoding);
			}
			else {
				throw new Exception("接口响应失败，请重新使用!");
			}
		}
		catch (UnknownHostException e) {
			throw new Exception("网络出现故障，请重新使用!", e);
		}
		finally {
			if (httpMethod != null) {
				try {
					httpMethod.releaseConnection();
				}
				catch (Exception e) {
				}
			}
		}
	}

	/**
	 * 1 HttpClientByPost
	 * @param checkURL
	 * @param paramsJson
	 * @return
	 * @throws Exception
	 */
	public static String HttpClientByPostNoKey(String checkURL, String paramsJson) throws Exception {
		String responseContent = "";
		CloseableHttpClient client = wrapClient();
		org.apache.log4j.Logger.getLogger(HttpClientUtil.class).info("向服务传递post的Json串是:" + paramsJson);

		HttpPost myPost = new HttpPost(checkURL);
		myPost.setHeader(HTTP.CONTENT_TYPE, "application/json;charset=utf-8");
		myPost.setHeader("charset", "utf-8");

		String authString = "posws:so*N9&@UyRsf!znj";
		byte[] authEncBytes = Base64.encodeBase64(authString.getBytes());
		String authStringEnc = new String(authEncBytes);
		myPost.setHeader("Authorization", "Basic " + authStringEnc);

		StringEntity s = new StringEntity(paramsJson, "utf-8");
		s.setContentEncoding("UTF-8");
		s.setContentType("application/json;charset=utf-8");
		myPost.setEntity(s);

		HttpResponse response = client.execute(myPost);

		org.apache.http.StatusLine status = response.getStatusLine();
		int statusCode = status.getStatusCode();
		if (statusCode != HttpStatus.SC_OK) {
			throw SystemException.getInstance(SystemErrorCode.MQ_COMMUNICATION_ERROR)
				.set("STATUS_LINE", response.getStatusLine().toString());
		}
		HttpEntity entity = response.getEntity();
		responseContent = EntityUtils.toString(entity);
		org.apache.log4j.Logger.getLogger(HttpClientUtil.class).info("post请求返回的是：" + responseContent + "-------");
		return responseContent;
	}

	public static CloseableHttpClient wrapClient() {
		try {
			SSLContext ctx = SSLContext.getInstance("TLSv1.2");
			X509TrustManager tm = new X509TrustManager() {

				@Override
				public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
					// TODO Auto-generated method stub
				}

				@Override
				public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
					// TODO Auto-generated method stub
				}

				@Override
				public X509Certificate[] getAcceptedIssuers() {
					// TODO Auto-generated method stub
					return null;
				}
			};
			ctx.init(null, new TrustManager[] { (TrustManager) tm }, null);
			SSLConnectionSocketFactory ssf = new SSLConnectionSocketFactory(ctx,
					SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
			CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssf).build();
			return httpclient;
		}
		catch (Exception ex) {
			ex.printStackTrace();
			return HttpClients.createDefault();
		}
	}

	/**
	 * 1 HttpClientByPost
	 * @param checkURL
	 * @param paramsJson
	 * @return
	 * @throws Exception
	 */
	public static String HttpsClientByPost(String checkURL, String paramsJson) throws Exception {
		String responseContent = "";
		// HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		CloseableHttpClient httpClient = HttpClientUtil.createSSLInsecureClient();
		HttpPost httpPost = new HttpPost(checkURL);

		try {
			LOG.info("请求地址" + checkURL + ":" + paramsJson);
			RequestConfig requestConfig = RequestConfig.custom()
				.setSocketTimeout(defaultSoTimeout)
				.setConnectTimeout(defaultConnectionTimeout)
				.setConnectionRequestTimeout(5000)
				.build();
			httpPost.setConfig(requestConfig);
			// paramsJson = URLEncoder.encode(paramsJson, "UTF-8") ;
			StringEntity entity = new StringEntity(paramsJson, "application/json", "UTF-8");

			httpPost.setEntity(entity);
			CloseableHttpResponse response = httpClient.execute(httpPost);
			BufferedReader in = null;
			try {
				InputStream content = response.getEntity().getContent();
				in = new BufferedReader(new InputStreamReader(content));
				StringBuilder sb = new StringBuilder();
				String line = "";
				while ((line = in.readLine()) != null) {
					sb.append(line);
				}
				responseContent = sb.toString();
				System.out.println("接收原始报文：" + URLDecoder.decode(responseContent, "UTF-8"));
			}
			finally {
				EntityUtils.consume(response.getEntity());
				response.close();
			}
			LOG.info("请求地址" + checkURL + "返回信息:" + responseContent);

		}
		catch (ConnectException e) {
			LOG.error("无法连接，调用地址:" + checkURL);
		}
		catch (ConnectTimeoutException e) {
			LOG.error("连接超时，调用地址:" + checkURL);
		}

		return responseContent;
	}

}

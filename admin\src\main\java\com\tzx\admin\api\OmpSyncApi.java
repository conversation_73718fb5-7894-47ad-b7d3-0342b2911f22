package com.tzx.admin.api;

import com.hccake.ballcat.common.model.result.R;
import com.tzx.admin.omp.SyncBusinessDataService;
import com.tzx.admin.omp.SyncMenuService;
import com.tzx.admin.omp.SyncPayToOmpService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

//@ConditionalOnProperty(prefix = "omp", name = "enabled", havingValue = "true")
@RequestMapping("ompSync")
@RestController
public class OmpSyncApi {

	final SyncBusinessDataService syncBusinessDataService;

	final SyncPayToOmpService syncPayToOmpService;

	private final SyncMenuService syncMenuService;

	public OmpSyncApi(SyncBusinessDataService syncBusinessDataService, SyncPayToOmpService syncPayToOmpService,
			SyncMenuService syncMenuService) {
		this.syncBusinessDataService = syncBusinessDataService;
		this.syncPayToOmpService = syncPayToOmpService;
		this.syncMenuService = syncMenuService;
	}

	@PostMapping("bill")
	public R<String> syncBill(@RequestParam(required = true) String month) {
		syncBusinessDataService.sync(month);
		return R.ok("SUCCESS");
	}

	@PostMapping("pay")
	public R<String> syncPay() {
		syncPayToOmpService.sync();
		return R.ok("SUCCESS");
	}

	/**
	 * 触发同步天子星支付方式到微生活
	 */
	@PostMapping("welifePay")
	public R<String> syncWelifePay() {
		syncPayToOmpService.syncWelifePay();
		return R.ok("SUCCESS");
	}

	@RequestMapping("menu")
	public R<String> syncMenu() {
		syncMenuService.sync();
		return R.ok("SUCCESS");
	}

}


-- 支付方式同步存贮过程
CREATE OR REPLACE PROCEDURE tzxerp.sync_pay_to_omp_rel_table()
    LANGUAGE plpgsql AS
$$
declare payment record;
declare exist_record Boolean;
BEGIN
    -- 打开针对erp_payments表的游标
FOR payment IN select t.id,t.ylc,t.fkfsbh from tzxerp.erp_payments t where t.id not in (select tzx_pay_id::bigint from tzxerp.erp_omp_pay_rel where tzx_pay_type = 'FK') LOOP
        -- 微信：2-- 支付宝：1-- 网商银行：613-- 美团外卖：992-- 饿了么外卖：996-- 美团团购券：7
        -- 如果 ylc=ERP_FKFS_ZFB,则 id = 1
            if payment.ylc = 'ERP_FKFS_ZFB' then --支付宝
               insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
               values (1,cast(payment.id as text) ,'FK','0',now(),now());
elseif payment.ylc = 'ERP_FKFS_WX' then -- 微信
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (2,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.ylc = 'ERP_FKFS_MT' then -- 美团券
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (7,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.ylc = 'ERP_FKFS_WSYH' then -- 网商银行
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (613,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.fkfsbh = '1986' then -- 美团外卖
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (992,cast(payment.id as text) ,'FK','0',now(),now());
            elseif payment.fkfsbh = '1985' then -- 饿了么外卖
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (996,cast(payment.id as text) ,'FK','0',now(),now());
else
                insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (nextval('omp_pay_rel_seq'),cast(payment.id as text) ,'FK','0',now(),now());
end if;
END LOOP;
        -- 映射关系表记录不存在则新加网商银行
select EXISTS(select tzx_pay_id::bigint from tzxerp.erp_omp_pay_rel where tzx_pay_type = 'FK' and omp_id=613) into exist_record;
IF  NOT exist_record THEN
        insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (613,'-1' ,'FK','0',now(),now());
END IF;
        -- 映射关系表记录不存在则新加中信银行
select EXISTS(select tzx_pay_id::bigint from tzxerp.erp_omp_pay_rel where tzx_pay_type = 'FK' and omp_id=614) into exist_record;
IF  NOT exist_record THEN
        insert into tzxerp.erp_omp_pay_rel(omp_id,tzx_pay_id,tzx_pay_type,sync_status,created_at,updated_at)
                values (614,'-2' ,'FK','0',now(),now());
END IF;
END
$$;

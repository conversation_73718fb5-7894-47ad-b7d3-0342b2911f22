package com.tzx.admin.omp.entity;

import lombok.Data;

@Data
public class OmpPayment implements java.io.Serializable {

	private Long pmid;

	private String pmno;

	private String pmname;

	private String pmnamesp;

	private Integer pmtype;

	private Integer isnoinvoiceflag;

	private Integer status;

	private Integer order;

	private Double money;

	private String ognids;

}
// OMP 支付方式格式
/*
 *
 * [ { "pmid": 1,---支付方式主键id int类型 "pmno": "1006",--支付方式编码 string类型 "pmname":
 * "支付宝支付",--支付方式名称 string类型 "pmnamesp": "ZFBZF",--支付方式名称首拼 string类型 "pmtype": 3,--支付方式类型
 * 类型1-现金类 2-会员消费类 3-移动支付 4-团购支付类 6-挂账类 7-银联卡类 8-代金券类 9-其他类 10-免单 12-银行卡类 13-快充卡类 14 墨博会员类
 * int类型 "isnointegralflag": 1,--是否参与积分 0-否 1-是 int类型 "isnochangeflag": 0,--是否找零 0-否 1-是
 * int类型 "isnocashboxflag": 0,--是否开钱箱 0-否 1-是 int类型 "isnoinvoiceflag": 1,--是否参与开发票 0--否
 * 1--是 int类型 "isnoshishouflag": 1,--是否计入实收 0-否 1-是 int类型 "status": 1, --支付方式状态 0-正常 1-停用
 * 2-逻辑删除 int类型 "order": 22,--排序 int类型 "ognids":
 * "9330a194-2adc-4b18-bf17-e786bbf26daf,9330a19a-766e-4f1f-8a65-f3024cc296cc,9330a189-ccc1-4d32-b4f2-d9be764f18bc"
 * ,--支付方式关联的门店id 逗号分隔 string类型 "ismustinput": 1,--盲交时是否必填 0-否 1-是 int类型 "money":
 * 1,--代金券的面值 double类型 "devicetype": 1,--适用设备 1-适用于PC-POS和移动POS 2-仅适用于PC-POS int类型
 * "isnotip": 1,--暂无用 "isnobookmoney": 1,--暂无用 "shishoupercent": 100,--计入实收的值 double
 * "shishoutype": 0,--实收类型 0-实收为面值的百分比 1-实收为固定值 int类型 "ispaymentremark": 0,--支付时需要是否添加备注
 * 0-否 1-是 string类型 "paymentremarkname": --自定义备注名称, string类型 "istipping": 0,--暂无用
 * "issplit": 0--是否拆账 0 不拆分 1 拆分 "updated_at": "2021-03-01 15:20:54",--更新时间 "created_at":
 * "2021-03-01 15:20:54",--创建时间 "delete_at": "2021-05-06 18:41:14",--删除时间 没有删除的时间的为空串 } ]
 *
 */
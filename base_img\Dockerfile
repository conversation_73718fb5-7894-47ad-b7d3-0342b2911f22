FROM adoptopenjdk/openjdk11:jdk-11.0.11_9-alpine

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk update
RUN apk update && apk upgrade && apk add ca-certificates && update-ca-certificates
RUN apk add --no-cache bash
RUN apk add ttf-dejavu fontconfig
RUN apk add --update tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
RUN rm -rf /var/cache/apk/*
RUN mkdir -p /usr/share/fonts/truetype/myfont
#RUN cp /build/scripts/chinesemsyh.ttf /usr/share/fonts/truetype/myfont/chinesemsyh.ttf

ENV TZ=Asia/Shanghai

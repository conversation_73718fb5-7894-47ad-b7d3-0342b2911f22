package com.tzx.admin.omp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("tzxerp.erp_call_omp_branch")
@Data
public class CallOmpBranchDayEndEntity {

	@TableField("createTime")
	private Date createTime;

	@TableField("result")
	private String result;

	@TableField("status")
	private Integer status;

	@TableField("bbrq")
	private String bbrq;

	@TableField("branchId")
	private String branchId;

	@TableField("mid")
	private String mid;

}

package com.tzx.admin.omp.event;

import com.alibaba.fastjson2.util.DateUtils;
import com.google.gson.Gson;
import com.tzx.admin.omp.OmpProperties;
import com.tzx.admin.omp.entity.CallOmpBranchDayEndEntity;
import com.tzx.admin.omp.entity.ErpOmpPosBillEntity;
import com.tzx.admin.omp.entity.OmpBranchPushEntity;
import com.tzx.admin.omp.entity.OmpResponse;
import com.tzx.admin.omp.mapper.CallOmpMapper;
import com.tzx.admin.omp.mapper.ErpOmpPosBillMapper;
import com.tzx.admin.utils.OmpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * omp事件监听
 */

public class OmpPushEventListener {

	private static final Logger LOG = LoggerFactory.getLogger(OmpPushEventListener.class);

	@Autowired
	private ErpOmpPosBillMapper erpOmpPosBillMapper;

	@Autowired
	private OmpProperties ompProperties;

	@Autowired
	private CallOmpMapper callOmpMapper;

	private static final Gson GSON = new Gson();

	@Transactional
	@Async
	@EventListener
	public void handlerEvent(CheckAllPushEvent checkAllPushEvent) {

		// 校验是否可以推送当天所有的数据
		Set<OmpBranchPushEntity> branchIds = checkAllPushEvent.getBranchIds();
		for (OmpBranchPushEntity branchId : branchIds) {

			Integer count = erpOmpPosBillMapper.countFrom(branchId);
			List<Integer> integers = erpOmpPosBillMapper.checkNoDayEnd(branchId);
			if (count > 0 && integers.isEmpty()) {

				try {
					String result = OmpUtils.syncOmpBranchNotice(ompProperties, branchId.getOmpBranchId(),
							branchId.getBbrq());
					CallOmpBranchDayEndEntity callOmpBranchDayEndEntity = new CallOmpBranchDayEndEntity();
					callOmpBranchDayEndEntity.setResult(result);
					callOmpBranchDayEndEntity.setBbrq(branchId.getBbrq());
					callOmpBranchDayEndEntity.setBranchId(branchId.getOmpBranchId());
					callOmpBranchDayEndEntity.setMid(ompProperties.getMerchantId());
					OmpResponse ompResponse = GSON.fromJson(result, OmpResponse.class);
					if (ompResponse == null || ompResponse.getCode() != 200) {
						callOmpBranchDayEndEntity.setStatus(0);
					}
					else {
						callOmpBranchDayEndEntity.setStatus(1);
					}
					callOmpMapper.insert(callOmpBranchDayEndEntity);
				}
				catch (Exception e) {
					LOG.warn("解析omp门店回调接口返回异常", e);
				}

			}
		}

	}

	@Transactional
	@Async
	@EventListener
	public void handlerShopCallBackEvent(ShopCallBackEvent shopCallBackEvent) {
		ErpOmpPosBillEntity erpOmpPosBillEntity = new ErpOmpPosBillEntity();
		erpOmpPosBillEntity.setJgxh(shopCallBackEvent.getJgxh());
		erpOmpPosBillEntity.setMid(ompProperties.getMerchantId());
		erpOmpPosBillEntity.setBbrq(DateUtils.parseDate(shopCallBackEvent.getBbrq()));
		erpOmpPosBillEntity.setBranchId(shopCallBackEvent.getOmpShopEntity().getBranchId());
		erpOmpPosBillEntity.setOmpBranchId(shopCallBackEvent.getOmpShopEntity().getOmpBranchId());
		erpOmpPosBillEntity.setOmpShopId(shopCallBackEvent.getOmpShopEntity().getShopOmpId());
		// 门店omp回调
		erpOmpPosBillEntity.setCallTime(new Date());
		String exec = shopCallBackEvent.exec();
		erpOmpPosBillEntity.setCallResult(exec);
		LOG.info("shopCallBackEvent:{} push omp result :{}", shopCallBackEvent, exec);
		OmpResponse ompResponse = null;
		try {
			erpOmpPosBillEntity.setCallTime(new Date());
			ompResponse = GSON.fromJson(exec, OmpResponse.class);
		}
		catch (Exception e) {
			LOG.warn("解析omp门店回调接口返回异常", e);
		}
		if (ompResponse == null || ompResponse.getCode() != 200) {
			erpOmpPosBillEntity.setCallStatus(0);
		}
		else {
			erpOmpPosBillEntity.setCallStatus(1);
		}
		erpOmpPosBillMapper.updatePosBill(erpOmpPosBillEntity);
		// 更新调用表
	}

}

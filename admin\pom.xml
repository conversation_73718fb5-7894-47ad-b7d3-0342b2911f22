<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>boh-boot</artifactId>
		<groupId>com.tzx.admin</groupId>
		<version>${revision}</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>admin</artifactId>

	<properties>
		<knife4j.version>3.0.3</knife4j.version>
		<tianai-captcha.version>1.4.1</tianai-captcha.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2</artifactId>
			<version>2.0.47</version>
		</dependency>
		<!-- 基于 spring authorization server 的授权服务器 -->
		<dependency>
			<groupId>com.hccake</groupId>
			<artifactId>ballcat-spring-security-oauth2-authorization-server</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.hccake</groupId>-->
<!--			<artifactId>ballcat-admin-core</artifactId>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.hccake</groupId>
			<artifactId>ballcat-admin-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>com.hccake</groupId>
			<artifactId>ballcat-spring-boot-starter-oss</artifactId>
		</dependency>

		<!--mysql驱动-->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>8.2.0</version>
		</dependency>

		<!-- openapi 扩展处理 -->
		<dependency>
			<groupId>com.hccake</groupId>
			<artifactId>ballcat-extend-openapi</artifactId>
		</dependency>
		<!-- springdoc swagger-ui -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-security</artifactId>
		</dependency>
		<!-- swagger 增强版 ui -->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-springdoc-ui</artifactId>
			<version>${knife4j.version}</version>
		</dependency>

		<!-- tianai 图形验证码 -->
		<dependency>
			<groupId>cloud.tianai.captcha</groupId>
			<artifactId>tianai-captcha-springboot-starter</artifactId>
			<version>${tianai-captcha.version}</version>
		</dependency>

		<!-- API, java.xml.bind module -->
		<!-- add it when jdk11 -->
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.26</version>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.0</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.6</version>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
		<extensions>
			<extension>
					<groupId>io.spring.javaformat</groupId>
					<artifactId>spring-javaformat-maven-plugin</artifactId>
			</extension>
		</extensions>
	</build>

</project>
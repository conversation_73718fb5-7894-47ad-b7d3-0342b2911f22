create table IF NOT EXISTS tzxerp.erp_omp_source
(
    tzx_source  varchar(30),
    omp_source  varchar(30),
    source_name varchar(30)
);

comment on table  tzxerp.erp_omp_source is '推送给omp的source对应码表';

create table IF NOT EXISTS tzxerp.erp_omp_pos_bill
(
    id          bigint not null,
    bbrq        date,
    mid         varchar(100),
    branchid    integer,
    ompbranchid varchar(100),
    jgxh        integer,
    ompshopid   varchar(100),
    ossstatus   integer,
    callstatus  integer,
    osstime     timestamp,
    calltime    timestamp,
    callresult  varchar(200),
    ossurl      varchar(400)
);


create table IF NOT EXISTS tzxerp.erp_call_omp_branch
(
    result     varchar(400),
    status     integer,
    bbrq       varchar(10),
    branchid   varchar(100),
    mid        varchar(100),
    createtime integer
);



create table IF NOT EXISTS tzxerp.erp_omp_oss
(
    id          bigint not null,
    type        varchar(50),
    url         varchar(400),
    create_time timestamp default now()
);


CREATE SEQUENCE IF NOT EXISTS TZXERP.erp_omp_pos_bill_seq
    INCREMENT BY 1
    START WITH 1
    NO MINVALUE
    NO MAXVALUE;




CREATE SEQUENCE IF NOT EXISTS TZXERP.erp_omp_oss_seq
    INCREMENT BY 1
    START WITH 1
    NO MINVALUE
    NO MAXVALUE;
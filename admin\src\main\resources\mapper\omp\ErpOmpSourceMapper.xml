<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.admin.omp.mapper.ErpOmpSourceMapper">
	<select id="queryOmpShopList" parameterType="com.tzx.admin.omp.entity.OmpShopEntity" resultType="com.tzx.admin.omp.entity.OmpShopEntity">
        select JGXH as shopId, eo.OMPID as shopOmpId, ed.ompid as ompBranchId,ed.id as branchId
        from TZXERP.ERP_ORGINFO eo
                 left join tzxerp.erp_orgbrand ed on eo.ppsx = ed.id
        <where>
            eo.ompid is not null
              and ed.ompid is not null
			<if	test="branchId != null and branchId != ''">
				and ed.ompid=#{branchId,jdbcType=VARCHAR}
			</if>
			<if test="shopOmpId != null and shopOmpId != ''">
				and eo.ompid = #{shopOmpId,jdbcType=VARCHAR}
			</if>
			<if test="shopId != null and shopId != ''">
				and eo.JGXH = #{shopId,jdbcType=INTEGER}
			</if>
        </where>
    </select>

</mapper>
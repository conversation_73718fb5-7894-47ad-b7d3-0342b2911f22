# 工作流基本信息
id: create_workflow
name: "创建自定义流程"
description: "创建自定义流程"
version: "1.0"
# 工作流规则
rules:
  - 分析要尽可能详细
  - 分析原生文件时必须阅读全部代码行
  - 生成的所有文档如果没有指定明确路径，则统一放在 {{work_path}} 目录下
  - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
  - 任务执行时，output内容必须遵照template模板格式
  - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
  - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务
# 工作步骤定义
tasks:
  - name: 询问用户想创建什么流程，与用户一起进行头脑风暴
    description: "我们要创建的流程是一套由不同AI角色驱动的流程，任务、步骤按顺序进行，通过将人类的工作过程拆分成不同的任务、步骤，让AI按照步骤以及明确的输入、输出、规则来完成原先由人来完成的工作，这个过程中人类只负责监督、反馈、验收，AI需要灵活切换各Ai角色来完成工作，为了避免AI的幻觉需要为AI提供足够的信息，因此我们需要创建合理的知识文档、过程文档、以及规范文档(guide)，这些文档会作为各任务、步骤的输入、输出，因此这个系统是文档驱动的。工作流是任务的集合，任务流是步骤的集合。"
    rules:
      - "请使用启发式的沟通模式，在相互的探讨中，找出最好的方案，需要突出AI在产品开发中的优势和创新点"
      - "你需要独立思考，充分表达不同意见，不要一味的顺从用户，因为用户的水平不如你，即使用户提出了反对意见，也需要展开反思和讨论"
      - "你需要思考：各AI角色之间如何衔？每个步骤需要什么样的输入文档，是？每个步骤应该产出什么样的输出文档，是？人类在什么时候需要进行监督和确认，需要核查哪些内容？"
      - "对于需要用户确认的节点，需要有明确的 用户确认点和需要确认的内容"
      - "只有用户允许写入文档时才可以写入，否则直接输出内容"
      - "用户确认后,将工作流写入output指定的文档，再执行 /work next 进入下一任务"
    input:
      - doc: 工作流模板 .agent/workflows/work/template.yml.tpl
      - doc: 任务流模板.agent/workflows/task/template.yml.tpl
    output:
      - doc: workflow_{{name}}.md
        template: |
          工作流程使用md格式，模板示例:
            任务列表:
              - 任务1:xx
                - 任务内容描述:
                  - xx
                - 输入:
                  - xx
                - 输出
                  - xx
                - 规则:
                  - xx
                - 执行者
                  - xx
                - 用户确认点
                  - xx
            xxx:
          任务流程使用md格式，模板示例:
            步骤列表:
              - 步骤1:xxx
                - 步骤内容描述:
                  - xx
                - 输入:
                  - xx
                - 输出
                  - xx
                - 规则:
                  - xx
                - 执行者
                  - xx
                - 用户确认点
                  - xx


  - name: 按格式创建流程文件
    rules:
      - "如果是工作流，需要读取 .agent/workflows/work/template.yml.tpl 模板, 根据模板来创建工作流文件，并保存到 用户指定目录的work/目录下"
      - "如果是任务流，需要读取 .agent/workflows/task/template.yml.tpl 模板, 根据模板来创建工作流文件，并保存到 用户指定目录的task/目录下"
    input:
      - doc: workflow_{{name}}.md
      - doc: .agent/workflows/work/template.yml.tpl
      - doc: .agent/workflows/task/template.yml.tpl
    output:
      - doc: .agent-local/workflows/{{task|work}}/{{workflow_name}}.yml

  - name: 测试流程
    rule:
      - "测试前请先提醒用户: 测试开始后，当前工作已结束，请关注 workspace/目录下创建的工作流测试文件，如果工作流需要修改，如果是工作流请直接修改workspace/xxx/work.yml，如果是任务流请直接修改workspace/task.yml，等流程测试正常后，请将修改内容同步到/workflows/目录下的流程模板中"
      - "然后执行 /work next 结束当前工作，然后再开始测试工作流"
      - "如果是工作流，请使用 /work use {{workflow_name}} 启动该流程"
      - "如果是任务流，请使用 /task use {{workflow_name}} 启动该流程"
      -

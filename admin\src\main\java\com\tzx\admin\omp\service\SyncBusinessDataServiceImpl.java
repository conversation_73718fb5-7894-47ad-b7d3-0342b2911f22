package com.tzx.admin.omp.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.util.DateUtils;
import com.google.gson.Gson;
import com.hccake.ballcat.common.oss.OssTemplate;
import com.tzx.admin.omp.OmpProperties;
import com.tzx.admin.omp.SyncBusinessDataService;
import com.tzx.admin.omp.entity.*;
import com.tzx.admin.omp.event.CheckAllPushEvent;
import com.tzx.admin.omp.event.ShopCallBackEvent;
import com.tzx.admin.omp.mapper.ErpBusinessDataMapper;
import com.tzx.admin.omp.mapper.ErpOmpPosBillMapper;
import com.tzx.admin.omp.mapper.OmpOssRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import static com.tzx.admin.constants.OmpConstant.NAME;
import static com.tzx.admin.constants.OmpConstant.OMP_ORDER_SUMMARY;
import static com.tzx.admin.enums.OmpOssEnums.ORDER;
import static com.tzx.admin.utils.OmpUtils.packageJson2OSSFile;

public class SyncBusinessDataServiceImpl implements SyncBusinessDataService {

	private final OssTemplate ossTemplate;

	private final OmpProperties ompProperties;

	private static final Gson GSON = new Gson();

	private final SqlSessionFactory sqlSessionFactory;

	private final ErpOmpPosBillMapper erpOmpPosBillMapper;

	private final ApplicationEventPublisher eventPublisher;

	private final ErpBusinessDataMapper erpBusinessDataMapper;

	private final Map<Integer, OmpShopEntity> tzxOmpShopMapping;

	private static final Logger LOGGER = LoggerFactory.getLogger(SyncBusinessDataServiceImpl.class);

	public SyncBusinessDataServiceImpl(OmpProperties ompProperties, ErpOmpPosBillMapper erpOmpPosBillMapper,
			ApplicationEventPublisher eventPublisher, ErpBusinessDataMapper erpBusinessDataMapper,
			SqlSessionFactory sqlSessionFactory, Map<Integer, OmpShopEntity> tzxOmpShopMapping,
			OssTemplate ossTemplate) {
		this.ossTemplate = ossTemplate;
		this.erpOmpPosBillMapper = erpOmpPosBillMapper;
		this.ompProperties = ompProperties;
		this.eventPublisher = eventPublisher;
		this.tzxOmpShopMapping = tzxOmpShopMapping;
		this.sqlSessionFactory = sqlSessionFactory;
		this.erpBusinessDataMapper = erpBusinessDataMapper;
	}

	@Override
	public void sync(String bbrq) {
		Set<OmpBranchPushEntity> branchIds = new HashSet<>();
		List<OmpOssRecordEntity> ompOssRecordEntityList = new ArrayList<>();
		// DONE add
		List<ErpShopData> erpShopData = erpBusinessDataMapper.queryDayEndNoOssData(bbrq);

		Map<String, List<ErpShopData>> collect = erpShopData.stream()
			.collect(Collectors.groupingBy(ErpShopData::getBbrq));
		collect.forEach((key, value) -> {
			for (ErpShopData shopData : value) {
				OmpShopEntity ompShopEntity = tzxOmpShopMapping.get(shopData.getJgxh());
				if (ompShopEntity == null || StringUtils.isBlank(ompShopEntity.getShopOmpId())) {
					LOGGER.warn("门店没有配置omp参数 jgxh:{}", shopData.getJgxh());
					continue;
				}
				branchIds.add(new OmpBranchPushEntity(key, ompShopEntity.getBranchId(), ompShopEntity.getOmpBranchId(),
						ompProperties.getMerchantId()));
				String ossKey = null;
				try {
					LOGGER.info("sync jgxh:{}, bbrq: {} start", shopData.getJgxh(), shopData.getBbrq());
					ossKey = ((SyncBusinessDataService) AopContext.currentProxy()).syncByDoor(shopData.getJgxh(),
							shopData.getBbrq(), ompShopEntity);
					LOGGER.info("sync jgxh:{}, bbrq: {} end", shopData.getJgxh(), shopData.getBbrq());
					if (ossKey == null) {
						continue;
					}
				}
				catch (Exception e) {
					ossKey = e.getMessage();
					LOGGER.error("门店{} 日期{} 推送omp账单异常", shopData.getJgxh(), shopData.getBbrq(), e);
					ompOssRecordEntityList.add(new OmpOssRecordEntity(ORDER.getType(), ossKey, new Date()));
					continue;
				}
				ompOssRecordEntityList.add(new OmpOssRecordEntity(ORDER.getType(), ossKey, new Date()));
				// 单门店推送
				eventPublisher.publishEvent(new ShopCallBackEvent(this, shopData.getJgxh(), key, ossKey, ompProperties,
						tzxOmpShopMapping.get(shopData.getJgxh())));
			}
			// 当前日期推送
			eventPublisher.publishEvent(new CheckAllPushEvent(this, branchIds));
		});
		batchInsertOssRecord(ompOssRecordEntityList);
	}

	@Transactional(rollbackFor = { IOException.class, RuntimeException.class })
	@Override
	public String syncByDoor(Integer jgxh, String bbrq, OmpShopEntity ompShopEntity) throws IOException {
		String ossKey = String.format(OMP_ORDER_SUMMARY, ompProperties.getMerchantId(), ompShopEntity.getOmpBranchId(),
				ompShopEntity.getShopOmpId(), bbrq);
		// 查询优惠
		List<ErpDisCountData> erpDisCountData = erpBusinessDataMapper.queryErpDisCountData(bbrq, jgxh);
		// 查询付款
		List<ErpPaymentData> erpPaymentData = erpBusinessDataMapper.queryErPaymentData(bbrq, jgxh);
		List<OmpBusinessDataEntity> ossDataObj = new ArrayList<>();
		if (!CollectionUtil.isEmpty(erpDisCountData)) {
			// DONE 生成优惠 json
			OmpBusinessDataEntity ompBusinessDataEntity = OmpBusinessDataEntity
				.genOmpEntityByDisCount(erpDisCountData.get(0));
			ossDataObj.add(ompBusinessDataEntity);
		}

		if (!CollectionUtil.isEmpty(erpPaymentData)) {
			// DONE 生成付款 json
			List<OmpBusinessDataEntity> collect = erpPaymentData.stream()
				.map(OmpBusinessDataEntity::genOmpEntityByPayment)
				.collect(Collectors.toList());
			ossDataObj.addAll(collect);
		}

		// DONE saveFile send oss generator record
		String ossDataObjStr = GSON.toJson(ossDataObj);
		LOGGER.info("sync jgxh:{}, bbrq: {}, 生成json数据{}", jgxh, bbrq, ossDataObjStr);
		File file = packageJson2OSSFile(ossDataObjStr);
		try {
			ossTemplate.deleteObject(NAME, ossKey);
			PutObjectResponse putObjectResponse = ossTemplate.putObject(NAME, ossKey, file);
			putObjectResponse.sdkHttpResponse();
		}
		catch (IOException e) {
			LOGGER.error("上传oss失败", e);
			return null;
		}
		Files.delete(file.toPath());
		ErpOmpPosBillEntity erpOmpPosBillEntity = new ErpOmpPosBillEntity();
		erpOmpPosBillEntity.setJgxh(jgxh);
		erpOmpPosBillEntity.setBbrq(DateUtils.parseDate(bbrq));
		erpOmpPosBillEntity.setBranchId(ompShopEntity.getBranchId());
		erpOmpPosBillEntity.setOmpBranchId(ompShopEntity.getOmpBranchId());
		erpOmpPosBillEntity.setOmpShopId(ompShopEntity.getShopOmpId());
		erpOmpPosBillEntity.setMid(ompProperties.getMerchantId());
		ErpOmpPosBillEntity erpOmpPosBillEntityData = erpOmpPosBillMapper
			.selectByErpOmpPosBillEntity(erpOmpPosBillEntity);
		erpOmpPosBillEntity.setOssUrl(NAME + "/" + ossKey);
		erpOmpPosBillEntity.setOssStatus(1);
		erpOmpPosBillEntity.setOssTime(new Date());
		if (erpOmpPosBillEntityData != null) {
			erpOmpPosBillMapper.updatePosBill(erpOmpPosBillEntity);
		}
		else {
			erpOmpPosBillMapper.InsertOmpPosBill(erpOmpPosBillEntity);
		}
		return NAME + "/" + ossKey;
	}

	public void batchInsertOssRecord(List<OmpOssRecordEntity> ompOssRecordEntityList) {

		try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
			OmpOssRecordMapper ompOssRecordMapper = sqlSession.getMapper(OmpOssRecordMapper.class);
			try {
				ompOssRecordMapper.batchInsertOssRecord(ompOssRecordEntityList);
				sqlSession.commit();
			}
			catch (Exception e) {
				sqlSession.rollback();
			}
		}
		catch (Exception ignore) {

		}
	}

}

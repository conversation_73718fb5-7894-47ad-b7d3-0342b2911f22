package com.tzx.admin.omp.service;

import com.tzx.admin.omp.entity.OmpBohIdRef;
import com.tzx.admin.omp.mapper.OmpBohIdRefMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class OmpBohIdRefService {

	@Autowired
	private OmpBohIdRefMapper ompBohIdRefMapper;

	@Transactional(rollbackFor = Exception.class)
	public Integer getOrCreateOmpId(String name, Integer brandId, Integer bohId, String... unionName) {
		// 1. 先查询是否存在
		OmpBohIdRef existingRef = ompBohIdRefMapper.findByNameAndBrandIdAndBohId(name, brandId, bohId);
		if (existingRef != null) {
			return existingRef.getOmpId();
		}

		// 2. 不存在，则查询当前name下最大的ompId
		Integer maxOmpId = ompBohIdRefMapper.findMaxOmpIdByName(unionName);

		// 3. 生成新的ompId
		int nextNumber = 1;
		if (maxOmpId != null) {
			nextNumber = maxOmpId + 1;
		}

		// 4. 插入新记录
		OmpBohIdRef newRef = new OmpBohIdRef();
		newRef.setName(name);
		newRef.setBrandId(brandId);
		newRef.setBohId(bohId);
		newRef.setOmpId(nextNumber);

		ompBohIdRefMapper.insert(newRef);

		return nextNumber;
	}

}
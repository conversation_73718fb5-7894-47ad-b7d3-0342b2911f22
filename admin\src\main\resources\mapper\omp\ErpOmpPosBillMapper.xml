<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.admin.omp.mapper.ErpOmpPosBillMapper">
	<resultMap id="BaseResultMap" type="com.tzx.admin.omp.entity.ErpOmpPosBillEntity">
		<result column="bbrq" property="bbrq" jdbcType="DATE" />
		<result column="mid" property="mid" jdbcType="VARCHAR" />
		<result column="branchid" property="branchId" jdbcType="BIGINT" />
		<result column="ompbranchid" property="ompBranchId" jdbcType="VARCHAR" />
		<result column="jgxh" property="jgxh" jdbcType="BIGINT" />
		<result column="ompshopid" property="ompShopId" jdbcType="VARCHAR" />
		<result column="ossstatus" property="ossStatus" jdbcType="VARCHAR" />
		<result column="callstatus" property="callStatus" jdbcType="VARCHAR" />
		<result column="calltime" property="callTime" jdbcType="TIMESTAMP" />
		<result column="osstime" property="ossTime" jdbcType="TIMESTAMP" />
		<result column="callresult" property="callResult" jdbcType="VARCHAR" />
		<result column="ossurl" property="ossUrl" jdbcType="VARCHAR" />
	</resultMap>

	<select id="selectByErpOmpPosBillEntity" resultMap="BaseResultMap" parameterType="com.tzx.admin.omp.entity.ErpOmpPosBillEntity">
		select id, bbrq, mid, branchid, ompbranchid, jgxh, ompshopid, ossstatus, callstatus, osstime, calltime, callresult, ossurl from tzxerp.erp_omp_pos_bill
		<where>
			<if test="bbrq != null">
				bbrq = #{bbrq}
			</if>

			<if test="mid != null and mid != ''">
				and MID = #{mid}
			</if>

			<if test="jgxh != null ">
				and JGXH = #{jgxh}
			</if>
			<if test="ompShopId != null and ompShopId != ''">
				and OMPSHOPID = #{ompShopId}
			</if>

			<if test="branchId != null ">
				and BRANCHID = #{branchId}
			</if>
			<if test="ompBranchId != null and ompBranchId != ''">
				and OMPBRANCHID = #{ompBranchId}
			</if>
		</where>
	</select>

	<insert id="InsertOmpPosBill" parameterType="com.tzx.admin.omp.entity.ErpOmpPosBillEntity">
		insert into tzxerp.erp_omp_pos_bill (id, bbrq, mid, branchid, ompbranchid, jgxh, ompshopid, ossstatus, callstatus, osstime, calltime, callresult, ossurl)
		values (nextval('TZXERP.erp_omp_pos_bill_seq'),#{bbrq},#{mid},#{branchId},#{ompBranchId},#{jgxh},#{ompShopId},#{ossStatus},#{callStatus},#{ossTime},#{callTime},#{callResult},#{ossUrl});
	</insert>

	<insert id="updatePosBill" parameterType="com.tzx.admin.omp.entity.ErpOmpPosBillEntity">
		update TZXERP.ERP_OMP_POS_BILL
		<set>
			<if test="ossStatus != null and ossStatus != ''">
				ossStatus = #{ossStatus},
			</if>
			<if test="callStatus != null ">
				callStatus = #{callStatus},
			</if>
			<if test="callResult != null and callResult != ''">
				callResult = #{callResult},
			</if>
			<if test="ossUrl != null and ossUrl != ''">
				ossUrl = #{ossUrl},
			</if>
			<if test="callTime != null">
				callTime = #{callTime}
			</if>
		</set>
		<where>
			JGXH = #{jgxh}
			AND OMPSHOPID = #{ompShopId}
			AND BRANCHID = #{branchId}
			AND OMPBRANCHID = #{ompBranchId}
			AND MID = #{mid}
			AND BBRQ = #{bbrq}
		</where>
	</insert>

	<select id="checkNoDayEnd" resultType="java.lang.Integer" parameterType="com.tzx.admin.omp.entity.OmpBranchPushEntity">
		select distinct shop.fdjgxh
		from (select ek.fdjgxh,ek.bbrq
			  from tzxerp.erp_fdzdk ek
					   left join TZXERP.ERP_ORGINFO eo
								 on ek.fdjgxh = eo.jgxh
			  where eo.ppsx = #{branchId} and bbrq = date'${bbrq}'
			  union all
			  select hk.fdjgxh,hk.bbrq
			  from tzxerp.his_fdzdk hk
					   left join TZXERP.ERP_ORGINFO eo
								 on hk.fdjgxh = eo.jgxh
			  where eo.ppsx =  #{branchId} and bbrq =date'${bbrq}') shop
				 inner join tzxerp.erp_omp_pos_bill el on shop.bbrq = el.bbrq and shop.fdjgxh = el.jgxh and (el.ossStatus is null or el.ossStatus !=1);
	</select>

	<select id="countFrom" resultType="java.lang.Integer" parameterType="com.tzx.admin.omp.entity.OmpBranchPushEntity">
		select count(*) from  tzxerp.erp_omp_pos_bill where ompbranchid = #{ompBranchId,jdbcType=VARCHAR} and bbrq = date'${bbrq}'
	</select>

</mapper>
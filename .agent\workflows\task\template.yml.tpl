name: "名称"
description: "描述"

# 任务目标列表，可通过 pre_steps 中执行脚本 task.py update来设定，设定后每个目标都会执行steps中的步骤
todos: []

# todos确定前对的前置步骤，一般用来确认并创建任务目标(todos)
pre_steps:
  - title: "询问用户要开发什么功能点"
    rule:
      - "如果有已知的工作任务，必须询问用户是否开发此工作任务"
      - "必须用户同意后，才能进入下一步 /task next"
  - title: "创建功能点开发工作流"
    rule:
      - "请根据用户描述，整理JSON_STRING，然后执行脚本 task.py update {{JSON_STRING}}"
      - JSON_STRING 模板: |
          "{\"title\": \"xxxx\", \"description\": \"xxxx\"}"

# 完成任务所需的步骤列表，可通过 /task reset 重置 steps所有步骤的状态，重新开始任务，例如 问题修复失败重新按步骤分析修复
steps:
  # 步骤
  - title: "收集信息"
    # 步骤的规则，一般定义了如何完成这个步骤、需要做的事情、要遵守的规则
    rule:
      - "询问用户所属模块"
      - "请用户提供需求文档、设计稿"
      - "查看 project.md 了解项目架构和相关知识库文档位置"
      - "使用 ls -R h5/src 搜索src目录中该模块现有的 views、workflow、store、api、test，找出该模块相关文件，与用户确认，用户确认后 使用 /task memory '{\"key\": \"value\"}' 记住这些信息"
      - "先查看测试用例及是否有要开发的功能点相关的用例，再对比实际代码实现，看看实现程度"
      - "需要用户确认"
    # AI输出的内容及格式要求
    output:
      - |
        请按需求信息报告模板输出:
        所属模块: xxx
        相关代码:
          - api:
            - src/api/xxx/xxx.ts
            - src/api/xxx/xxx.ts
          - workflow:
            - src/xxx/xxx.ts
          - store:
            - src/xxx/xxx.ts
          - test:
            - src/xxx/xxx.test.ts
          - view:
            - src/xxx/xxx.vue
            - src/xxx/xxx.vue
        相关文档:
          - 需求文档: xxx.md
          - 规范文档:
            - api: _agent-local/knowledge/guidelines/api-guide.md
            - workflow: _agent-local/knowledge/guidelines/workflow-guide.md
            - store: _agent-local/knowledge/guidelines/store-guide.md
            - test: _agent-local/knowledge/guidelines/test-guide.md
            - view: _agent-local/knowledge/guidelines/view-guide.md
        相关用例及逻辑:
        - xxx
        实际代码实现程度:
        - workflow:
          - 已实现: xxx
          - 未实现: xxx
        - store:
          - 已实现: xxx
          - 未实现: xxx
        - api:
          - 已实现: xxx
          - 未实现: xxx
        - views:
          - 已实现: xxx
          - 未实现: xxx

  - title: "设计测试用例、实际代码的整体实现方案"
    rule:
      - "需要与用户确认"
      - "需要评估是否涉及修改测试用例"
      - "先阅读、分析现有的用例、代码，在当前基础上进行设计"
      - "要在现有代码的基础上给出精准的修改方案"
      - "修改的代码必须符合现有代码的风格、结构、规范(guide文档)"
      - "不要删除现有的注释，除非注释的代码也被整体删除了"
      - "等待用户确认方案，并提醒用户是否需要写入到 output doc 指定的文件中，覆盖原有内容"
    output:
      - doc: -agent-local/workspace/feature_design.md
        请按需遵循以下格式输出: |
          # xxxx实现方案

          ## 一、变更内容概述

          1. Store层变更
          - xx

          2. Workflow层变更
          - xx

          3. 视图层变更
          - xx

          4. 测试用例变更
          - xx

          ## 二、具体实现

          ### 1. Store层 (h5/src/store/**.ts)

          ```typescript
          ```

          ### 2. Workflow层 (h5/src/workflow/**.ts)

          ```typescript
          /**
          * 详细业务逻辑注释
          */
          每当('xxx', {}, async () => {

          })

          ```

          ### 3. 视图层

          #### 3.1 xxx组件 (h5/src/views/**.vue)

          ```vue
          ```

          ### 4. 测试用例 (h5/src/__tests__/**.test.ts)

          ```typescript
            xxxx

            // ... 其他测试用例保持不变
          })
          ```

          ## 三、实现效果

          1. xxx功能
            - 添加规格（名称、价格、会员价）
            - 删除规格
            - 规格数量限制（最多20个）
            - 默认规格管理
            - 时价菜规格管理

          2. 交互体验
            - 底部弹出的规格表单
            - 表单验证
            - 操作反馈（Toast提示）
            - 平滑的动画效果

          3. 代码质量
            - 符合项目规范
            - 职责分离清晰
            - 测试覆盖完整
            - 代码可维护性高



  - title: "进行方案评审"
    worker: reviewer
    input:
      - doc: -agent-local/workspace/feature_design.md
      - doc: _agent-local/workspace/dish-manager/android_source_code.md
    rule:
      - "需要与用户确认"
      - "需要先阅读原生代码文件，分析安卓原生代码中的业务逻辑"
      - "从全局角度分析当前修改涉及到的文件，列出文件列表"
      - "阅读上述文件，然后从全局角度分析上述修改方案造成的影响，包括对现有业务逻辑的影响，以及对测试用例的影响"
      - "阅读相关guide文档，分析上述修改是否符合规范和指引"
      - "是否删除了不相关的代码"
      - "是否遗漏了测试用例"
      - "如果用户要求整理一份新的完整版方案，请在上一步设计的基础上整理，不要引入未取得用户许可的变动"
    output:
      - 请按以下要求分析、整理方案: |
          # 方案评审

          ## 安卓原生代码中的业务逻辑
          - 是否阅读了安卓原生代码中的业务逻辑
          - 原业务逻辑是什么？
          - 跟当前的方案有什么功能点差异？

          ## workflow
          - 是否阅读了workflow/文件夹下的相关workflow
          - 是否符合 workflow-guide.md 规范

          ## store
          - 是否阅读了store/文件夹下的相关store
          - 是否符合 store-guide.md 规范

          ## api
          - 是否阅读了api/文件夹下的相关接口
          - 是否符合 api-guide.md 规范

          ## view
          - 是否符合 view-guide.md 规范
          - 是否阅读了views/文件夹下的相关视图文件

          ## test
          - 是否阅读了__test__/文件夹下的相关测试文件
          - 是否符合 test-guide.md 规范
          - 是否符合 test-guide.md 中的测试用例模板结构
          - 每个用例是否正确模拟了数据、状态、接口
          - 是否有遗漏的测试用例
          - 边界情况是否完整


  - title: "执行修改"
    worker: developer
    input:
      - doc: -agent-local/workspace/feature_design.md
    rule:
      - "必须严格按照feature_design.md执行修改"
      - "不可删除现有的注释，除非注释没有用了"
      - "修改完需要用户确认"

  - title: "执行测试，然后直接进入下一步"
    rule:
      - "测试方式:执行 'cd h5 && pnpm test:run'"
      - "执行完测试脚本后，直接进入下一步"

  - title: "需要根据我们当前的任务场景和功能逻辑来判断分析是要修改用例还是业务代码，找出问题根因，梳理出问题列表,然后让用户选择要修复的问题"
    worker: developer
    rule:
      - "需用用户确认"
      - "展示详细的问题现象"
      - "定位并列出相关文件"
      - "使用 cat 命令批量查看罗列出的相关文件"
      - "全面理解业务逻辑和测试用例的预期"
      - "分析并列出问题根因：可能是代码实现不符合设计、接口参数不匹配、状态更新错误或测试用例预期不一致等等"
      - "询问用户是否启动 /test fixbug 进入问题修复流程，需要用户确认，如果用户要进入问题修复流程则先查看下当前的任务记忆，然后再执行 /task use fixbug 指令进入问题修复流程，然后再执行 /task memory '{\"key\": \"value\"}' 指令记住当前任务记忆"

  - title: "校验实际代码与设计方案的一致性"
    rule:
      - "校验测试用例是否有偏差"

  - title: "提交代码"
    rule:
      - "提交代码前需要询问用户提供tapd链接"
      - "必须在h5目录下提交代码"
      - commit信息格式为: |
          feat: {{message}}{{tapd_link}}
